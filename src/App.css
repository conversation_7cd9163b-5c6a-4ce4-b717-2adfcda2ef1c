@import url("https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500;600;700&family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-neutral-50 text-neutral-900 font-sans;
    font-family: "Inter", "Noto Sans Devanagari", sans-serif;
  }
}

@layer components {
  .rajasthani-pattern {
    background-image: radial-gradient(
        circle at 25% 25%,
        rgba(230, 42, 129, 0.1) 1px,
        transparent 1px
      ),
      radial-gradient(
        circle at 75% 75%,
        rgba(240, 92, 3, 0.1) 1px,
        transparent 1px
      );
    background-size:
      20px 20px,
      20px 20px;
    background-position:
      0 0,
      10px 10px;
  }

  .bandhani-dots {
    background-image: radial-gradient(
      circle at 50% 50%,
      currentColor 1px,
      transparent 1px
    );
    background-size: 8px 8px;
  }

  .mirror-work-border {
    border-image: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 3px,
        rgba(212, 175, 55, 0.3) 3px,
        rgba(212, 175, 55, 0.3) 6px
      )
      1;
  }

  .gradient-rajasthani {
    background: linear-gradient(135deg, #fdf6e3 0%, #f5f5dc 50%, #f4a460 100%);
  }

  .text-hindi {
    font-family: "Noto Sans Devanagari", Arial, sans-serif;
    font-feature-settings:
      "kern" 1,
      "liga" 1;
  }

  .heading-decorative {
    font-family: "Playfair Display", serif;
    font-weight: 600;
  }

  .whatsapp-float {
    position: fixed;
    bottom: 80px;
    right: 20px;
    z-index: 1000;
    width: 60px;
    height: 60px;
    background-color: #25d366;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 4px 20px rgba(37, 211, 102, 0.4);
    transition: all 0.3s ease;
  }

  .whatsapp-float:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(37, 211, 102, 0.6);
  }

  .trust-badge {
    @apply inline-flex items-center gap-2 px-3 py-1 bg-white border border-neutral-200 rounded-full text-sm font-medium text-neutral-700;
  }

  .cta-primary {
    @apply bg-rajasthani-pink hover:bg-rajasthani-pink/90 text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 active:scale-95;
  }

  .cta-secondary {
    @apply bg-rajasthani-orange hover:bg-rajasthani-orange/90 text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105 active:scale-95;
  }

  .category-card {
    @apply relative overflow-hidden rounded-xl bg-white shadow-sm hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1;
  }

  .product-card {
    @apply bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden;
  }
}
