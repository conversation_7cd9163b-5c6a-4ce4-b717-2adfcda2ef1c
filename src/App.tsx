import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { LanguageProvider } from "./contexts/LanguageContext";
import { Header } from "./components/layout/Header.tsx";
import { Footer } from "./components/layout/Footer";
import { BottomNav } from "./components/layout/BottomNav";
import { WhatsAppChat } from "./components/ui/whatsapp-chat";
import Index from "./pages/Index";
import ProductListing from "./pages/ProductListing";
import ProductDetail from "./pages/ProductDetail";
import CustomTailoring from "./pages/CustomTailoring";
import StyleCatalog from "./pages/StyleCatalog";
import MeasurementGuide from "./pages/MeasurementGuide";
import About from "./pages/About";
import Contact from "./pages/Contact";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <LanguageProvider>
        <BrowserRouter>
          <div className="min-h-screen bg-neutral-50 pb-16 lg:pb-0">
            <Header />

            <main>
              <Routes>
                <Route path="/" element={<Index />} />
                <Route
                  path="/category/:category"
                  element={<ProductListing />}
                />
                <Route path="/product/:id" element={<ProductDetail />} />
                <Route path="/tailoring" element={<CustomTailoring />} />
                <Route path="/style-catalog" element={<StyleCatalog />} />
                <Route
                  path="/measurement-guide"
                  element={<MeasurementGuide />}
                />
                <Route path="/about" element={<About />} />
                <Route path="/contact" element={<Contact />} />
                {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </main>

            <Footer />
            <BottomNav />
            <WhatsAppChat />
          </div>
        </BrowserRouter>
      </LanguageProvider>
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
