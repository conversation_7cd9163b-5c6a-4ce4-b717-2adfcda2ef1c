import { Link } from "react-router-dom";
import {
  Facebook,
  Instagram,
  MessageCircle,
  Mail,
  Phone,
  MapPin,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { TrustBadges } from "@/components/ui/trust-badges";
import { useLanguage } from "@/contexts/LanguageContext";

export function Footer() {
  const { t } = useLanguage();

  const quickLinks = [
    { name: t("nav.about"), href: "/about" },
    { name: "Shipping & Returns", href: "/shipping" },
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "Size Guide", href: "/size-guide" },
    { name: "Care Instructions", href: "/care" },
  ];

  const categories = [
    { name: t("nav.rajputi"), href: "/category/rajputi" },
    { name: t("nav.comforters"), href: "/category/comforters" },
    { name: t("nav.sarees"), href: "/category/sarees" },
    { name: t("nav.suits"), href: "/category/suits" },
    { name: t("nav.linens"), href: "/category/linens" },
    { name: t("nav.tailoring"), href: "/tailoring" },
  ];

  return (
    <footer className="bg-neutral-100 border-t border-neutral-200">
      {/* Trust Badges Section */}
      <div className="bg-white py-6 border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4">
          <TrustBadges variant="horizontal" className="justify-center" />
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <div>
              <h3 className="heading-decorative text-lg font-bold text-rajasthani-pink">
                Bayawala Emporium
              </h3>
              <p className="text-sm text-hindi text-neutral-600">
                बयावाला एम्पोरियम
              </p>
            </div>
            <p className="text-sm text-neutral-600">{t("footer.about")}</p>
            <p className="text-sm font-medium text-rajasthani-pink">
              {t("footer.trusted")}
            </p>

            {/* Social Links */}
            <div className="flex space-x-3">
              <a
                href="https://instagram.com/bayawalaemporium"
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-neutral-200 hover:bg-rajasthani-pink hover:text-white rounded-full transition-colors"
              >
                <Instagram className="h-4 w-4" />
              </a>
              <a
                href="https://facebook.com/bayawalaemporium"
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-neutral-200 hover:bg-rajasthani-pink hover:text-white rounded-full transition-colors"
              >
                <Facebook className="h-4 w-4" />
              </a>
              <a
                href="https://wa.me/************"
                target="_blank"
                rel="noopener noreferrer"
                className="p-2 bg-neutral-200 hover:bg-green-500 hover:text-white rounded-full transition-colors"
              >
                <MessageCircle className="h-4 w-4" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="font-semibold text-neutral-900">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <Link
                    to={link.href}
                    className="text-sm text-neutral-600 hover:text-rajasthani-pink transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div className="space-y-4">
            <h4 className="font-semibold text-neutral-900">Categories</h4>
            <ul className="space-y-2">
              {categories.map((category) => (
                <li key={category.href}>
                  <Link
                    to={category.href}
                    className="text-sm text-neutral-600 hover:text-rajasthani-pink transition-colors"
                  >
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h4 className="font-semibold text-neutral-900">
              {t("footer.contact")}
            </h4>
            <div className="space-y-3">
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 text-rajasthani-pink mt-0.5 flex-shrink-0" />
                <div className="text-sm text-neutral-600">
                  <p>Bhadra Fort Road,</p>
                  <p>Bhadra, Ahmedabad</p>
                  <p>Gujarat 380001, India</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Phone className="h-4 w-4 text-rajasthani-pink" />
                <a
                  href="tel:+************"
                  className="text-sm text-neutral-600 hover:text-rajasthani-pink"
                >
                  +91 98765 43210
                </a>
              </div>

              <div className="flex items-center gap-3">
                <Mail className="h-4 w-4 text-rajasthani-pink" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-sm text-neutral-600 hover:text-rajasthani-pink"
                >
                  <EMAIL>
                </a>
              </div>
            </div>

            {/* WhatsApp Support */}
            <div className="pt-2">
              <Button
                size="sm"
                className="bg-green-500 hover:bg-green-600 text-white w-full"
                onClick={() =>
                  window.open("https://wa.me/************", "_blank")
                }
              >
                <MessageCircle className="h-4 w-4 mr-2" />
                WhatsApp Support
              </Button>
            </div>
          </div>
        </div>

        {/* Newsletter Signup */}
        <div className="mt-12 pt-8 border-t border-neutral-200">
          <div className="max-w-md mx-auto text-center">
            <h4 className="font-semibold text-neutral-900 mb-2">
              Stay Updated with New Collections
            </h4>
            <p className="text-sm text-neutral-600 mb-4">
              Get exclusive offers and be the first to know about new arrivals
            </p>
            <div className="flex gap-2">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-2 border border-neutral-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-rajasthani-pink"
              />
              <Button className="cta-primary px-6">Subscribe</Button>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="bg-neutral-200 py-4">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
            <p className="text-sm text-neutral-600 text-center sm:text-left">
              © 2024 Bayawala Emporium. All rights reserved.
            </p>
            <div className="flex items-center gap-4 text-xs text-neutral-500">
              <span>Secure Checkout</span>
              <span>•</span>
              <span>SSL Protected</span>
              <span>•</span>
              <span>Trusted Seller</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
