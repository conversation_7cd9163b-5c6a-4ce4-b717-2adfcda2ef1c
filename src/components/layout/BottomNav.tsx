import { Link, useLocation } from "react-router-dom";
import { Home, Grid3X3, ShoppingCart, User, Heart } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/contexts/LanguageContext";

export function BottomNav() {
  const location = useLocation();
  const { t } = useLanguage();

  const navigation = [
    {
      name: t("nav.home"),
      href: "/",
      icon: Home,
    },
    {
      name: "Categories",
      href: "/categories",
      icon: Grid3X3,
    },
    {
      name: "Cart",
      href: "/cart",
      icon: ShoppingCart,
      badge: 0,
    },
    {
      name: "Wishlist",
      href: "/wishlist",
      icon: Heart,
    },
    {
      name: "Account",
      href: "/account",
      icon: User,
    },
  ];

  const isActive = (href: string) => {
    if (href === "/") {
      return location.pathname === "/";
    }
    return location.pathname.startsWith(href);
  };

  return (
    <div className="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-neutral-200 lg:hidden">
      <nav className="flex items-center justify-around px-2 py-2">
        {navigation.map((item) => {
          const Icon = item.icon;
          const active = isActive(item.href);

          return (
            <Link
              key={item.name}
              to={item.href}
              className={`flex flex-col items-center justify-center px-3 py-2 min-w-0 flex-1 relative ${
                active
                  ? "text-rajasthani-pink"
                  : "text-neutral-600 hover:text-rajasthani-pink"
              }`}
            >
              <div className="relative">
                <Icon className="h-5 w-5 mb-1" />
                {item.badge !== undefined && item.badge > 0 && (
                  <Badge className="absolute -top-2 -right-2 h-4 w-4 flex items-center justify-center p-0 bg-rajasthani-pink text-white text-xs">
                    {item.badge}
                  </Badge>
                )}
              </div>
              <span className="text-xs font-medium truncate w-full text-center">
                {item.name}
              </span>
              {active && (
                <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-8 h-0.5 bg-rajasthani-pink rounded-full" />
              )}
            </Link>
          );
        })}
      </nav>
    </div>
  );
}
