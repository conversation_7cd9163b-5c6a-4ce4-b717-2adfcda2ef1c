import { useState } from "react";
import { Link, useLocation } from "react-router-dom";
import { Menu, Search, ShoppingCart, User, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { LanguageToggle } from "@/components/ui/language-toggle";
import { useLanguage } from "@/contexts/LanguageContext";
import { Badge } from "@/components/ui/badge";

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const { t } = useLanguage();
  const location = useLocation();

  const navigation = [
    { name: t("nav.home"), href: "/", key: "nav.home" },
    { name: t("nav.rajputi"), href: "/category/rajputi", key: "nav.rajputi" },
    {
      name: t("nav.comforters"),
      href: "/category/comforters",
      key: "nav.comforters",
    },
    { name: t("nav.sarees"), href: "/category/sarees", key: "nav.sarees" },
    { name: t("nav.suits"), href: "/category/suits", key: "nav.suits" },
    { name: t("nav.linens"), href: "/category/linens", key: "nav.linens" },
    { name: t("nav.tailoring"), href: "/tailoring", key: "nav.tailoring" },
    { name: t("nav.about"), href: "/about", key: "nav.about" },
    { name: t("nav.contact"), href: "/contact", key: "nav.contact" },
  ];

  const isActive = (href: string) => location.pathname === href;

  return (
    <>
      {/* Main Header */}
      <header className="sticky top-0 z-50 w-full bg-white border-b border-neutral-200 shadow-sm">
        <div className="flex items-center justify-between px-4 py-3">
          {/* Left: Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMenuOpen(true)}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Center: Logo */}
          <Link
            to="/"
            className="flex-1 flex justify-center lg:justify-start lg:flex-none"
          >
            <div className="text-center lg:text-left">
              <h1 className="heading-decorative text-lg lg:text-xl font-bold text-rajasthani-pink">
                Bayawala Emporium
              </h1>
              <p className="text-xs text-neutral-600 text-hindi">
                बयावाला एम्पोरियम
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-8 mx-8">
            {navigation.slice(0, 6).map((item) => (
              <Link
                key={item.key}
                to={item.href}
                className={`text-sm font-medium transition-colors hover:text-rajasthani-pink ${
                  isActive(item.href)
                    ? "text-rajasthani-pink border-b-2 border-rajasthani-pink pb-1"
                    : "text-neutral-700"
                }`}
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Right: Actions */}
          <div className="flex items-center space-x-2">
            {/* Language Toggle */}
            <LanguageToggle />

            {/* Search Button */}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsSearchOpen(true)}
              className="hidden sm:flex"
            >
              <Search className="h-5 w-5" />
            </Button>

            {/* Cart */}
            <Link to="/cart">
              <Button variant="ghost" size="sm" className="relative">
                <ShoppingCart className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 bg-rajasthani-pink text-white text-xs">
                  0
                </Badge>
              </Button>
            </Link>

            {/* Account */}
            <Link to="/account">
              <Button variant="ghost" size="sm" className="hidden sm:flex">
                <User className="h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Mobile Menu Overlay */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 lg:hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsMenuOpen(false)}
          />
          <div className="fixed top-0 left-0 h-full w-80 bg-white shadow-xl">
            <div className="flex items-center justify-between p-4 border-b">
              <h2 className="heading-decorative text-lg font-semibold text-rajasthani-pink">
                Menu
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMenuOpen(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            <nav className="p-4 space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.key}
                  to={item.href}
                  onClick={() => setIsMenuOpen(false)}
                  className={`block px-4 py-3 rounded-lg text-sm font-medium transition-colors ${
                    isActive(item.href)
                      ? "bg-rajasthani-pink/10 text-rajasthani-pink"
                      : "text-neutral-700 hover:bg-neutral-100"
                  }`}
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Mobile Menu Footer */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t bg-neutral-50">
              <div className="flex items-center justify-between">
                <Link to="/account" onClick={() => setIsMenuOpen(false)}>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-2"
                  >
                    <User className="h-4 w-4" />
                    Account
                  </Button>
                </Link>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setIsSearchOpen(true);
                    setIsMenuOpen(false);
                  }}
                  className="flex items-center gap-2"
                >
                  <Search className="h-4 w-4" />
                  Search
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Search Overlay */}
      {isSearchOpen && (
        <div
          className="fixed inset-0 z-50 bg-black/50"
          onClick={() => setIsSearchOpen(false)}
        >
          <div className="fixed top-0 left-0 right-0 bg-white p-4 shadow-lg">
            <div className="max-w-2xl mx-auto">
              <div className="flex items-center gap-3">
                <Search className="h-5 w-5 text-neutral-400" />
                <input
                  type="text"
                  placeholder="Search for products..."
                  className="flex-1 text-lg outline-none"
                  autoFocus
                />
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsSearchOpen(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
