import { useLanguage } from "@/contexts/LanguageContext";
import { Button } from "@/components/ui/button";
import { Globe } from "lucide-react";

export function LanguageToggle() {
  const { language, setLanguage } = useLanguage();

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setLanguage(language === "en" ? "hi" : "en")}
      className="flex items-center gap-2 text-neutral-700 hover:text-rajasthani-pink"
    >
      <Globe className="h-4 w-4" />
      <span className="font-medium">{language === "en" ? "हिंदी" : "EN"}</span>
    </Button>
  );
}
