import { MessageCircle } from "lucide-react";

export function WhatsAppChat() {
  const handleWhatsAppClick = () => {
    // Replace with actual WhatsApp business number
    const phoneNumber = "919876543210";
    const message = "Hello! I am interested in your Rajasthani textiles.";
    const url = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
    window.open(url, "_blank");
  };

  return (
    <button
      onClick={handleWhatsAppClick}
      className="whatsapp-float"
      aria-label="Chat on WhatsApp"
    >
      <MessageCircle className="h-7 w-7 text-white" />
    </button>
  );
}
