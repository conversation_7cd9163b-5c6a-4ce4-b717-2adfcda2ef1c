import {
    Shield,
    Truck,
    RotateCcw,
    CreditCard,
    CheckCircle,
  } from "lucide-react";
  import { useLanguage } from "@/contexts/LanguageContext";
  
  interface TrustBadgesProps {
    variant?: "horizontal" | "grid";
    className?: string;
  }
  
  export function TrustBadges({
    variant = "horizontal",
    className = "",
  }: TrustBadgesProps) {
    const { t } = useLanguage();
  
    const badges = [
      {
        icon: Truck,
        text: t("trust.freeshipping"),
        color: "text-green-600",
      },
      {
        icon: CreditCard,
        text: t("trust.cod"),
        color: "text-blue-600",
      },
      {
        icon: Shield,
        text: t("trust.secure"),
        color: "text-rajasthani-orange",
      },
      {
        icon: RotateCcw,
        text: t("trust.returns"),
        color: "text-purple-600",
      },
      {
        icon: CheckCircle,
        text: t("trust.quality"),
        color: "text-rajasthani-pink",
      },
    ];
  
    const containerClass =
      variant === "grid"
        ? "grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3"
        : "flex flex-wrap justify-center gap-3";
  
    return (
      <div className={`${containerClass} ${className}`}>
        {badges.map((badge, index) => (
          <div key={index} className="trust-badge">
            <badge.icon className={`h-4 w-4 ${badge.color}`} />
            <span className="text-xs sm:text-sm whitespace-nowrap">
              {badge.text}
            </span>
          </div>
        ))}
      </div>
    );
  }
  