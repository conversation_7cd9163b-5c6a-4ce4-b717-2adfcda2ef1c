import { Star, ShoppingCart, Heart } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useLanguage } from "@/contexts/LanguageContext";

interface ProductCardProps {
  id: string;
  image: string;
  title: string;
  price: number;
  originalPrice?: number;
  rating: number;
  reviewCount: number;
  isCODAvailable?: boolean;
  isFreeShipping?: boolean;
  discount?: number;
  onAddToCart?: (id: string) => void;
  onToggleWishlist?: (id: string) => void;
  isWishlisted?: boolean;
}

export function ProductCard({
  id,
  image,
  title,
  price,
  originalPrice,
  rating,
  reviewCount,
  isCODAvailable = true,
  isFreeShipping = true,
  discount,
  onAddToCart,
  onToggleWishlist,
  isWishlisted = false,
}: ProductCardProps) {
  const { t } = useLanguage();

  return (
    <div className="product-card group cursor-pointer">
      <div className="relative overflow-hidden">
        <img
          src={image}
          alt={title}
          className="w-full h-48 sm:h-56 object-cover transition-transform duration-300 group-hover:scale-105"
        />

        {/* Badges */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          {discount && (
            <Badge className="bg-rajasthani-pink text-white text-xs">
              {discount}% OFF
            </Badge>
          )}
          {isFreeShipping && (
            <Badge
              variant="secondary"
              className="bg-green-100 text-green-800 text-xs"
            >
              {t("trust.freeshipping")}
            </Badge>
          )}
        </div>

        {/* Wishlist button */}
        <button
          onClick={(e) => {
            e.stopPropagation();
            onToggleWishlist?.(id);
          }}
          className="absolute top-2 right-2 p-2 rounded-full bg-white/80 hover:bg-white transition-colors"
        >
          <Heart
            className={`h-4 w-4 ${isWishlisted ? "fill-rajasthani-pink text-rajasthani-pink" : "text-neutral-600"}`}
          />
        </button>

        {/* Quick actions overlay */}
        <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
          <Button
            size="sm"
            className="cta-primary"
            onClick={(e) => {
              e.stopPropagation();
              onAddToCart?.(id);
            }}
          >
            <ShoppingCart className="h-4 w-4 mr-2" />
            {t("product.addtocart")}
          </Button>
        </div>
      </div>

      <div className="p-3 space-y-2">
        <h3 className="font-medium text-sm sm:text-base text-neutral-900 line-clamp-2 group-hover:text-rajasthani-pink transition-colors">
          {title}
        </h3>

        {/* Rating */}
        <div className="flex items-center gap-1">
          <div className="flex items-center">
            {[...Array(5)].map((_, i) => (
              <Star
                key={i}
                className={`h-3 w-3 ${
                  i < Math.floor(rating)
                    ? "fill-yellow-400 text-yellow-400"
                    : "text-neutral-300"
                }`}
              />
            ))}
          </div>
          <span className="text-xs text-neutral-600">
            {rating} ({reviewCount})
          </span>
        </div>

        {/* Price */}
        <div className="flex items-center gap-2">
          <span className="font-semibold text-lg text-neutral-900">
            ₹{price.toLocaleString()}
          </span>
          {originalPrice && (
            <span className="text-sm text-neutral-500 line-through">
              ₹{originalPrice.toLocaleString()}
            </span>
          )}
        </div>

        {/* Features */}
        <div className="flex items-center gap-2 text-xs">
          {isCODAvailable && (
            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded">
              {t("trust.cod")}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}
