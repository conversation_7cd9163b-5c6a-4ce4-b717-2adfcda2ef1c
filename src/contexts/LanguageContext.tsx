import React, { createContext, useContext, useState, ReactNode } from "react";

export type Language = "en" | "hi";

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const translations = {
  en: {
    // Navigation
    "nav.home": "Home",
    "nav.rajputi": "Rajputi Dresses",
    "nav.comforters": "Comforters",
    "nav.sarees": "Sarees",
    "nav.suits": "Suits",
    "nav.linens": "Home Linen",
    "nav.tailoring": "Custom Tailoring",
    "nav.about": "About Us",
    "nav.contact": "Contact",

    // Homepage
    "hero.title": "Bayawala Emporium – Heritage Weaves, Modern Vibes",
    "hero.subtitle":
      "Step into the Colors of Rajasthan – Shop Authentic Rajputi Poshak & Home Textiles!",
    "hero.cta": "Shop Rajputi Poshak",
    "welcome.text":
      "Welcome to Bayawala Emporium – Where Rajasthani Tradition meets Modern Fashion!",

    // Product Actions
    "product.shopnow": "Shop Now",
    "product.addtocart": "Add to Cart",
    "product.buynow": "Buy Now",
    "product.viewdetails": "View Details",

    // Trust Badges
    "trust.freeshipping": "Free Shipping",
    "trust.cod": "COD Available",
    "trust.secure": "Secure Checkout",
    "trust.returns": "7 Day Returns",
    "trust.quality": "Quality Guaranteed",

    // Categories
    "category.rajputi.title": "Rajputi Dresses",
    "category.rajputi.desc":
      "Discover our collection of elegant Rajputi Poshak with intricate mirrorwork and vibrant colors.",
    "category.comforters.title": "Comforters",
    "category.comforters.desc":
      "Handcrafted quilts and comforters with traditional Rajasthani patterns.",
    "category.sarees.title": "Sarees",
    "category.sarees.desc":
      "Beautiful Bandhani and block-print sarees from the heart of Rajasthan.",
    "category.suits.title": "Suits & Kurtas",
    "category.suits.desc": "Traditional suits and kurtas with modern styling.",
    "category.linens.title": "Home Linen",
    "category.linens.desc":
      "Transform your home with our authentic Rajasthani textile collection.",

    // Footer
    "footer.about": "Authentic Rajasthani Textiles since 1975",
    "footer.trusted": "Trusted by 50,000+ customers",
    "footer.contact": "Contact Us",
    "footer.social": "Follow Us",

    // Forms
    "form.submit": "Submit",
    "form.name": "Name",
    "form.email": "Email",
    "form.phone": "Phone",
    "form.message": "Message",

    // About
    "about.heritage.title": "Our Heritage",
    "about.heritage.text":
      "Founded in 1975 in Bhadra, Rajasthan, Bayawala Emporium is a family-run emporium of traditional Rajasthani apparel and home textiles.",
    "about.craft.title": "Our Craft",
    "about.craft.text":
      "Each piece is handpicked or handcrafted with care. Our artisans use age-old Rajputi techniques – vibrant Bandhani dyes, intricate gota work, mirror embroidery.",
    "about.mission.title": "Our Mission",
    "about.mission.text":
      "We bring royal Rajasthani style to your home, with a modern, eco-friendly twist. Quality and authenticity are our promise.",

    // Tailoring
    "tailoring.title": "Custom Tailoring Service",
    "tailoring.desc":
      "Get a custom-fit Rajputi Poshak or suit made to your measurements.",
    "tailoring.measurements": "Submit Measurements",
    "tailoring.style": "Choose Style",
    "tailoring.guide": "Measurement Guide",
  },
  hi: {
    // Navigation
    "nav.home": "होम",
    "nav.rajputi": "राजपूती पोशाक",
    "nav.comforters": "रजाई सेट",
    "nav.sarees": "साड़ियाँ",
    "nav.suits": "सूट-कुर्ते",
    "nav.linens": "होम लिनेन",
    "nav.tailoring": "कस्टम सिलाई",
    "nav.about": "हमारे बारे में",
    "nav.contact": "संपर्क करें",

    // Homepage
    "hero.title": "बयावाला एम्पोरियम – विरासत संग आधुनिकता",
    "hero.subtitle":
      "राजस्थान के रंगों में आपका स्वागत है – खरीदें प्रामाणिक राजपूती पोशाक एवं होम टेक्सटाइल्स",
    "hero.cta": "राजपूती पोशाक देख��ं",
    "welcome.text":
      "स्वागत है Bayawala Emporium में – जहां राजस्थानी परंपरा मिलती है आधुनिक फैशन से!",

    // Product Actions
    "product.shopnow": "अभी खरीदें",
    "product.addtocart": "कार्ट में जोड़ें",
    "product.buynow": "तुरंत खरीदें",
    "product.viewdetails": "विवरण देखें",

    // Trust Badges
    "trust.freeshipping": "मुफ्त डिलीवरी",
    "trust.cod": "COD उपलब्ध",
    "trust.secure": "सुरक्षित चेकआउट",
    "trust.returns": "7 दिन रिटर्न",
    "trust.quality": "गुणवत्ता गारंटी",

    // Categories
    "category.rajputi.title": "राजपूती पोशाक",
    "category.rajputi.desc":
      "जटिल दर्पण कार्य और जीवंत रंगों के साथ राजपूती पोशाक का हमारा संग्रह खोजें।",
    "category.comforters.title": "रजाई सेट",
    "category.comforters.desc":
      "पारंपरिक राजस्थानी ���ैटर्न के साथ हस्तनिर्मित रजाई और कंबल।",
    "category.sarees.title": "साड़ियाँ",
    "category.sarees.desc":
      "राजस्थान के दिल से सुंदर बांधनी और ब्लॉक-प्रिंट साड़ियाँ।",
    "category.suits.title": "सूट और कुर्ते",
    "category.suits.desc": "आधुनिक स्टाइलिंग के साथ पारंपरिक सूट और कुर्ते।",
    "category.linens.title": "होम लिनेन",
    "category.linens.desc":
      "हमारे प्रामाणिक राजस्थानी वस्त्र संग्रह के साथ अपने घर को बदलें।",

    // Footer
    "footer.about": "1975 से प्रामाणिक राजस्थानी वस्त्र",
    "footer.trusted": "50,000+ ग्राहकों का भरोसा",
    "footer.contact": "संपर्क करें",
    "footer.social": "हमें फॉलो करें",

    // Forms
    "form.submit": "सबमिट करें",
    "form.name": "नाम",
    "form.email": "ईमेल",
    "form.phone": "फोन",
    "form.message": "संदेश",

    // About
    "about.heritage.title": "हमारी विरासत",
    "about.heritage.text":
      "1975 में भद्रा, राजस्थान में स्थापित, बयावाला एम्पोरियम पारंपरिक राजस्थानी परिधान और गृह वस्त्रों का एक पारिवारिक व्यापार है।",
    "about.craft.title": "हमारी कला",
    "about.craft.text":
      "प्रत्येक वस्तु को सावधानी से चुना या हस्तनिर्मित किया जाता है। हमारे कारीगर पुरानी राजपूत तकनीकों का उपयोग करते हैं।",
    "about.mission.title": "हमारा मिशन",
    "about.mission.text":
      "हम शाही राजस्थानी शैली को आपके घर में लाते हैं, आधुनिक, पर्यावरण-अनुकूल मोड़ के साथ।",

    // Tailoring
    "tailoring.title": "कस्टम सिलाई सेवा",
    "tailoring.desc": "अपने माप के अनुसार राजपूती पोशाक या सूट सिलवाएं।",
    "tailoring.measurements": "माप सबमिट करें",
    "tailoring.style": "स्टाइल चुनें",
    "tailoring.guide": "माप गाइड",
  },
};

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined,
);

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState<Language>("en");

  const t = (key: string): string => {
    return translations[language][key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LanguageProvider");
  }
  return context;
}
