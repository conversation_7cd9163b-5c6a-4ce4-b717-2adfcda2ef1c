import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  Ruler,
  <PERSON><PERSON>,
  Sc<PERSON><PERSON>,
  Clock,
  CheckCircle,
  ArrowRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

export default function CustomTailoring() {
  const { t } = useLanguage();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState({
    garmentType: "",
    measurements: {},
    specialRequests: "",
    contactInfo: {},
  });

  const steps = [
    {
      id: 1,
      title: "Select Garment",
      description: "Choose what you want to get tailored",
      icon: Palette,
    },
    {
      id: 2,
      title: "Measurements",
      description: "Provide your accurate measurements",
      icon: Ruler,
    },
    {
      id: 3,
      title: "Style & Details",
      description: "Customize your design preferences",
      icon: Scissors,
    },
    {
      id: 4,
      title: "Contact & Submit",
      description: "Final details and submission",
      icon: CheckCircle,
    },
  ];

  const garmentTypes = [
    {
      id: "rajputi-poshak",
      name: "Rajputi Poshak",
      description: "Traditional Rajasthani dress with intricate work",
      price: "₹15,000 - ₹25,000",
      timeline: "15-20 days",
    },
    {
      id: "lehenga-choli",
      name: "Lehenga Choli",
      description: "Custom designed lehenga with blouse",
      price: "₹12,000 - ₹30,000",
      timeline: "12-18 days",
    },
    {
      id: "suit-set",
      name: "Suit Set",
      description: "Kurta, palazzo/churidar and dupatta set",
      price: "₹8,000 - ₹15,000",
      timeline: "10-15 days",
    },
    {
      id: "blouse",
      name: "Blouse",
      description: "Custom fitted blouse for sarees",
      price: "₹3,000 - ₹8,000",
      timeline: "7-10 days",
    },
    {
      id: "home-textiles",
      name: "Home Textiles",
      description: "Comforters, curtains, cushion covers",
      price: "₹5,000 - ₹15,000",
      timeline: "8-12 days",
    },
  ];

  const measurementFields = {
    "rajputi-poshak": [
      { name: "bust", label: "Bust/Chest (छाती)", unit: "inches" },
      { name: "waist", label: "Waist (कमर)", unit: "inches" },
      { name: "hip", label: "Hip (कूल्हे)", unit: "inches" },
      { name: "shoulder", label: "Shoulder (कंधे)", unit: "inches" },
      { name: "armLength", label: "Arm Length (बाजू)", unit: "inches" },
      { name: "poshakLength", label: "Poshak Length (लम्बाई)", unit: "inches" },
      { name: "neckDepth", label: "Neck Depth (गला)", unit: "inches" },
    ],
    "lehenga-choli": [
      { name: "bust", label: "Bust/Chest (छाती)", unit: "inches" },
      { name: "waist", label: "Waist (कमर)", unit: "inches" },
      { name: "hip", label: "Hip (कूल्हे)", unit: "inches" },
      {
        name: "lehengaLength",
        label: "Lehenga Length (घाघरा)",
        unit: "inches",
      },
      { name: "choliLength", label: "Choli Length (चोली)", unit: "inches" },
      { name: "sleeveLength", label: "Sleeve Length (आस्तीन)", unit: "inches" },
    ],
  };

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                Choose Your Garment Type
              </h2>
              <p className="text-neutral-600">
                Select what you would like to get custom tailored
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {garmentTypes.map((garment) => (
                <Card
                  key={garment.id}
                  className={`cursor-pointer transition-all hover:shadow-lg ${
                    formData.garmentType === garment.id
                      ? "border-rajasthani-pink bg-rajasthani-pink/5"
                      : "border-neutral-200"
                  }`}
                  onClick={() =>
                    setFormData({ ...formData, garmentType: garment.id })
                  }
                >
                  <CardHeader>
                    <CardTitle className="text-lg">{garment.name}</CardTitle>
                    <CardDescription>{garment.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex justify-between text-sm">
                      <span className="font-medium text-rajasthani-pink">
                        {garment.price}
                      </span>
                      <span className="text-neutral-600 flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {garment.timeline}
                      </span>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        );

      case 2:
        const fields =
          measurementFields[
            formData.garmentType as keyof typeof measurementFields
          ] || [];
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                Your Measurements
              </h2>
              <p className="text-neutral-600 mb-4">
                Please provide accurate measurements for the best fit
              </p>
              <Link
                to="/measurement-guide"
                className="text-rajasthani-pink hover:underline text-sm flex items-center gap-1"
              >
                Need help with measurements? View our guide
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {fields.map((field) => (
                <div key={field.name} className="space-y-2">
                  <Label htmlFor={field.name}>{field.label}</Label>
                  <div className="flex">
                    <Input
                      id={field.name}
                      type="number"
                      placeholder="Enter measurement"
                      className="flex-1"
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          measurements: {
                            ...formData.measurements,
                            [field.name]: e.target.value,
                          },
                        })
                      }
                    />
                    <span className="ml-2 px-3 py-2 bg-neutral-100 border border-l-0 rounded-r-lg text-sm text-neutral-600">
                      {field.unit}
                    </span>
                  </div>
                </div>
              ))}
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-800">
                <strong>Tip:</strong> For the most accurate measurements, we
                recommend having someone help you measure or visiting a local
                tailor. All measurements should be taken while wearing
                well-fitted undergarments.
              </p>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                Style & Special Requests
              </h2>
              <p className="text-neutral-600">
                Add any special design preferences or requirements
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="neckStyle">Neck Style (optional)</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose neck style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="round">Round Neck</SelectItem>
                    <SelectItem value="v-neck">V-Neck</SelectItem>
                    <SelectItem value="boat">Boat Neck</SelectItem>
                    <SelectItem value="square">Square Neck</SelectItem>
                    <SelectItem value="deep-v">Deep V-Neck</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="sleeveStyle">Sleeve Style (optional)</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose sleeve style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="full">Full Sleeve</SelectItem>
                    <SelectItem value="three-quarter">3/4 Sleeve</SelectItem>
                    <SelectItem value="half">Half Sleeve</SelectItem>
                    <SelectItem value="sleeveless">Sleeveless</SelectItem>
                    <SelectItem value="bell">Bell Sleeve</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="embroidery">Embroidery Preference</Label>
                <Select>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose embroidery style" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="gota">Gota Work</SelectItem>
                    <SelectItem value="mirror">Mirror Work</SelectItem>
                    <SelectItem value="thread">Thread Embroidery</SelectItem>
                    <SelectItem value="beads">Bead Work</SelectItem>
                    <SelectItem value="minimal">Minimal Work</SelectItem>
                    <SelectItem value="heavy">Heavy Work</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="specialRequests">Special Requests</Label>
                <Textarea
                  id="specialRequests"
                  placeholder="Any specific requirements, color preferences, or design details you'd like to mention..."
                  value={formData.specialRequests}
                  onChange={(e) =>
                    setFormData({
                      ...formData,
                      specialRequests: e.target.value,
                    })
                  }
                  rows={4}
                />
              </div>
            </div>

            <Link
              to="/style-catalog"
              className="inline-block text-rajasthani-pink hover:underline"
            >
              Browse our style catalog for design inspiration →
            </Link>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                Contact Information
              </h2>
              <p className="text-neutral-600">
                We'll use this information to coordinate your custom order
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input id="name" placeholder="Enter your full name" required />
              </div>

              <div>
                <Label htmlFor="phone">Phone Number *</Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+91 XXXXX XXXXX"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="address">Delivery Address *</Label>
                <Textarea
                  id="address"
                  placeholder="Complete address with city, state, and pincode"
                  required
                />
              </div>
            </div>

            <div className="bg-rajasthani-pink/5 p-6 rounded-lg border border-rajasthani-pink/20">
              <h3 className="font-semibold text-neutral-900 mb-4">
                Order Summary
              </h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Garment Type:</span>
                  <span className="font-medium">
                    {garmentTypes.find((g) => g.id === formData.garmentType)
                      ?.name || "Not selected"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Estimated Price:</span>
                  <span className="font-medium">
                    {garmentTypes.find((g) => g.id === formData.garmentType)
                      ?.price || "TBD"}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Timeline:</span>
                  <span className="font-medium">
                    {garmentTypes.find((g) => g.id === formData.garmentType)
                      ?.timeline || "TBD"}
                  </span>
                </div>
              </div>
            </div>

            <div className="text-sm text-neutral-600 space-y-2">
              <p>
                <strong>What happens next:</strong>
              </p>
              <ol className="list-decimal list-inside space-y-1 ml-4">
                <li>We'll review your measurements and requirements</li>
                <li>
                  Our team will call you within 24 hours to confirm details
                </li>
                <li>We'll provide exact pricing and timeline</li>
                <li>Once confirmed, we'll start crafting your custom piece</li>
                <li>Regular updates will be shared via WhatsApp</li>
              </ol>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="max-w-4xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="text-center mb-8">
        <h1 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
          {t("tailoring.title")}
        </h1>
        <p className="text-lg text-neutral-600 max-w-2xl mx-auto">
          {t("tailoring.desc")}
        </p>
      </div>

      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {steps.map((step, index) => {
            const Icon = step.icon;
            const isActive = currentStep === step.id;
            const isCompleted = currentStep > step.id;

            return (
              <div key={step.id} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center border-2 transition-colors ${
                      isActive
                        ? "border-rajasthani-pink bg-rajasthani-pink text-white"
                        : isCompleted
                          ? "border-green-500 bg-green-500 text-white"
                          : "border-neutral-300 text-neutral-400"
                    }`}
                  >
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="text-center mt-2">
                    <p
                      className={`text-sm font-medium ${isActive ? "text-rajasthani-pink" : "text-neutral-600"}`}
                    >
                      {step.title}
                    </p>
                    <p className="text-xs text-neutral-500 hidden sm:block">
                      {step.description}
                    </p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`flex-1 h-0.5 mx-4 ${isCompleted ? "bg-green-500" : "bg-neutral-300"}`}
                  />
                )}
              </div>
            );
          })}
        </div>
      </div>

      {/* Step Content */}
      <Card className="mb-8">
        <CardContent className="p-8">{renderStepContent()}</CardContent>
      </Card>

      {/* Navigation */}
      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={handlePrevious}
          disabled={currentStep === 1}
        >
          Previous
        </Button>

        <div className="flex gap-2">
          {currentStep < steps.length ? (
            <Button
              onClick={handleNext}
              disabled={currentStep === 1 && !formData.garmentType}
              className="cta-primary"
            >
              Next Step
            </Button>
          ) : (
            <Button className="cta-primary">
              {t("tailoring.measurements")}
            </Button>
          )}
        </div>
      </div>

      {/* Help Section */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="text-center">
            <Ruler className="h-8 w-8 text-rajasthani-pink mx-auto mb-2" />
            <CardTitle className="text-lg">Need Help Measuring?</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-neutral-600 mb-4">
              Step-by-step measurement guide with diagrams and videos
            </p>
            <Link to="/measurement-guide">
              <Button variant="outline" size="sm">
                View Guide
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <Palette className="h-8 w-8 text-rajasthani-orange mx-auto mb-2" />
            <CardTitle className="text-lg">Browse Styles</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-neutral-600 mb-4">
              Explore our catalog of stitching patterns and designs
            </p>
            <Link to="/style-catalog">
              <Button variant="outline" size="sm">
                Style Catalog
              </Button>
            </Link>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="text-center">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <CardTitle className="text-lg">Expert Support</CardTitle>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-sm text-neutral-600 mb-4">
              Our tailoring experts are here to help you
            </p>
            <Button
              variant="outline"
              size="sm"
              onClick={() =>
                window.open("https://wa.me/919876543210", "_blank")
              }
            >
              WhatsApp Us
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
