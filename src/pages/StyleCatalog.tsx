import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useLanguage } from "@/contexts/LanguageContext";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

export default function StyleCatalog() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState("");

  const styleCategories = {
    necklines: {
      title: "Neckline Styles",
      description: "Choose from various neckline designs",
      styles: [
        {
          id: "round-neck",
          name: "Round Neck",
          nameHindi: "गोल गला",
          image:
            "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=200&h=250&fit=crop&crop=top",
          description: "Classic round neckline suitable for all occasions",
          suitableFor: ["<PERSON><PERSON><PERSON> Poshak", "<PERSON><PERSON>", "Blouses"],
        },
        {
          id: "v-neck",
          name: "V-<PERSON>",
          nameHindi: "वी नेक",
          image:
            "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=250&fit=crop&crop=top",
          description: "Elegant V-shaped neckline for a modern look",
          suitableFor: ["Blouses", "Kurtas", "Kurtis"],
        },
        {
          id: "boat-neck",
          name: "Boat Neck",
          nameHindi: "बोट नेक",
          image:
            "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=200&h=250&fit=crop&crop=top",
          description: "Wide horizontal neckline showing shoulders gracefully",
          suitableFor: ["Blouses", "Crop Tops"],
        },
        {
          id: "square-neck",
          name: "Square Neck",
          nameHindi: "चौकोर गला",
          image:
            "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=250&fit=crop&crop=top",
          description: "Modern square-shaped neckline with clean lines",
          suitableFor: ["Blouses", "Dresses"],
        },
        {
          id: "deep-v",
          name: "Deep V-Neck",
          nameHindi: "डीप वी नेक",
          image:
            "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=250&fit=crop&crop=top",
          description: "Bold deep V-neckline for special occasions",
          suitableFor: ["Party Blouses", "Wedding Wear"],
        },
        {
          id: "halter",
          name: "Halter Neck",
          nameHindi: "हाल्टर नेक",
          image:
            "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=250&fit=crop&crop=top",
          description: "Trendy halter style with neck tie",
          suitableFor: ["Modern Blouses", "Crop Tops"],
        },
      ],
    },
    sleeves: {
      title: "Sleeve Styles",
      description: "Various sleeve designs for your garment",
      styles: [
        {
          id: "full-sleeve",
          name: "Full Sleeve",
          nameHindi: "पूरी आस्तीन",
          image:
            "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=200&h=250&fit=crop&crop=center",
          description: "Traditional full-length sleeves",
          suitableFor: ["All garments"],
        },
        {
          id: "three-quarter",
          name: "3/4 Sleeve",
          nameHindi: "3/4 आस्तीन",
          image:
            "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=250&fit=crop&crop=center",
          description: "Comfortable three-quarter length sleeves",
          suitableFor: ["Kurtas", "Blouses"],
        },
        {
          id: "half-sleeve",
          name: "Half Sleeve",
          nameHindi: "हाफ आस्तीन",
          image:
            "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=200&h=250&fit=crop&crop=center",
          description: "Classic half-sleeve design",
          suitableFor: ["Casual wear", "Summer garments"],
        },
        {
          id: "sleeveless",
          name: "Sleeveless",
          nameHindi: "बिना आस्तीन",
          image:
            "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=250&fit=crop&crop=center",
          description: "No sleeves for hot weather comfort",
          suitableFor: ["Blouses", "Summer tops"],
        },
        {
          id: "bell-sleeve",
          name: "Bell Sleeve",
          nameHindi: "बेल आस्तीन",
          image:
            "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=250&fit=crop&crop=center",
          description: "Flared bell-shaped sleeves for ethnic look",
          suitableFor: ["Kurtas", "Indo-western wear"],
        },
        {
          id: "puff-sleeve",
          name: "Puff Sleeve",
          nameHindi: "पफ आस्तीन",
          image:
            "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=250&fit=crop&crop=center",
          description: "Voluminous puffed sleeves for royal appearance",
          suitableFor: ["Traditional wear", "Party wear"],
        },
      ],
    },
    backs: {
      title: "Back Designs",
      description: "Beautiful back designs for blouses and tops",
      styles: [
        {
          id: "simple-back",
          name: "Simple Back",
          nameHindi: "सादी पीठ",
          image:
            "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=200&h=250&fit=crop&crop=center",
          description: "Classic simple back design",
          suitableFor: ["All occasions"],
        },
        {
          id: "tie-back",
          name: "Tie-Back",
          nameHindi: "बांधने वाली पीठ",
          image:
            "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=250&fit=crop&crop=center",
          description: "Adjustable tie-back design",
          suitableFor: ["Blouses", "Crop tops"],
        },
        {
          id: "button-back",
          name: "Button Back",
          nameHindi: "बटन वाली पीठ",
          image:
            "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=200&h=250&fit=crop&crop=center",
          description: "Traditional button closure at back",
          suitableFor: ["Formal blouses", "Traditional wear"],
        },
        {
          id: "zipper-back",
          name: "Zipper Back",
          nameHindi: "ज़िप वाली पीठ",
          image:
            "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=250&fit=crop&crop=center",
          description: "Modern zipper closure",
          suitableFor: ["Modern blouses", "Western wear"],
        },
        {
          id: "open-back",
          name: "Open Back",
          nameHindi: "खुली पीठ",
          image:
            "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=250&fit=crop&crop=center",
          description: "Stylish open back design",
          suitableFor: ["Party wear", "Special occasions"],
        },
        {
          id: "criss-cross",
          name: "Criss-Cross Back",
          nameHindi: "क्रिस-क्रॉस पीठ",
          image:
            "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=250&fit=crop&crop=center",
          description: "Trendy criss-cross strap design",
          suitableFor: ["Modern blouses", "Fusion wear"],
        },
      ],
    },
    embroidery: {
      title: "Embroidery Patterns",
      description: "Traditional Rajasthani embroidery styles",
      styles: [
        {
          id: "gota-work",
          name: "Gota Work",
          nameHindi: "गोटा वर्क",
          image:
            "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=250&fit=crop&crop=center",
          description: "Traditional gold ribbon embroidery",
          suitableFor: ["Wedding wear", "Festival outfits"],
        },
        {
          id: "mirror-work",
          name: "Mirror Work",
          nameHindi: "मिरर वर्क",
          image:
            "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=200&h=250&fit=crop&crop=center",
          description: "Sparkling mirror embellishments",
          suitableFor: ["Traditional wear", "Dance costumes"],
        },
        {
          id: "thread-embroidery",
          name: "Thread Embroidery",
          nameHindi: "धागे की कढ़ाई",
          image:
            "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=200&h=250&fit=crop&crop=center",
          description: "Intricate hand-stitched thread work",
          suitableFor: ["All occasions"],
        },
        {
          id: "bead-work",
          name: "Bead Work",
          nameHindi: "मोती का काम",
          image:
            "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=250&fit=crop&crop=center",
          description: "Elegant bead embellishments",
          suitableFor: ["Party wear", "Bridal outfits"],
        },
        {
          id: "zardozi",
          name: "Zardozi Work",
          nameHindi: "ज़रदोज़ी वर्क",
          image:
            "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=250&fit=crop&crop=center",
          description: "Royal metallic thread embroidery",
          suitableFor: ["Bridal wear", "Luxury garments"],
        },
        {
          id: "bandhani",
          name: "Bandhani Pattern",
          nameHindi: "बांधनी पैटर्न",
          image:
            "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=250&fit=crop&crop=center",
          description: "Traditional tie-dye patterns",
          suitableFor: ["Casual wear", "Festival outfits"],
        },
      ],
    },
  };

  const filteredStyles = (styles: any[]) => {
    if (!searchQuery) return styles;
    return styles.filter(
      (style) =>
        style.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        style.nameHindi.includes(searchQuery) ||
        style.description.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link
          to="/tailoring"
          className="inline-flex items-center text-rajasthani-pink hover:underline mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tailoring Form
        </Link>

        <h1 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
          Style Catalog
        </h1>
        <p className="text-lg text-neutral-600 max-w-3xl">
          {t("tailoring.style")} चुनें पसंदीदा सिलाई शैली - Browse our
          collection of traditional and modern stitching patterns to customize
          your perfect garment.
        </p>
      </div>

      {/* Search */}
      <div className="mb-8">
        <div className="relative max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-neutral-400" />
          <Input
            type="text"
            placeholder="Search styles..."
            className="pl-10"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {/* Style Categories */}
      <Tabs defaultValue="necklines" className="w-full">
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-8">
          <TabsTrigger value="necklines">Necklines</TabsTrigger>
          <TabsTrigger value="sleeves">Sleeves</TabsTrigger>
          <TabsTrigger value="backs">Back Designs</TabsTrigger>
          <TabsTrigger value="embroidery">Embroidery</TabsTrigger>
        </TabsList>

        {Object.entries(styleCategories).map(([categoryKey, category]) => (
          <TabsContent
            key={categoryKey}
            value={categoryKey}
            className="space-y-6"
          >
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                {category.title}
              </h2>
              <p className="text-neutral-600">{category.description}</p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {filteredStyles(category.styles).map((style) => (
                <div
                  key={style.id}
                  className="group bg-white rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 overflow-hidden border border-neutral-200"
                >
                  <div className="aspect-[4/5] overflow-hidden">
                    <img
                      src={style.image}
                      alt={style.name}
                      className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>

                  <div className="p-4">
                    <h3 className="font-semibold text-neutral-900 mb-1">
                      {style.name}
                    </h3>
                    <p className="text-sm text-rajasthani-pink font-medium mb-2 text-hindi">
                      {style.nameHindi}
                    </p>
                    <p className="text-sm text-neutral-600 mb-3 line-clamp-2">
                      {style.description}
                    </p>

                    <div className="mb-3">
                      <p className="text-xs text-neutral-500 mb-1">
                        Suitable for:
                      </p>
                      <div className="flex flex-wrap gap-1">
                        {style.suitableFor.slice(0, 2).map((item, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-neutral-100 text-xs rounded-full text-neutral-600"
                          >
                            {item}
                          </span>
                        ))}
                        {style.suitableFor.length > 2 && (
                          <span className="px-2 py-1 bg-neutral-100 text-xs rounded-full text-neutral-600">
                            +{style.suitableFor.length - 2}
                          </span>
                        )}
                      </div>
                    </div>

                    <Button
                      size="sm"
                      variant="outline"
                      className="w-full group-hover:border-rajasthani-pink group-hover:text-rajasthani-pink"
                    >
                      Select This Style
                    </Button>
                  </div>
                </div>
              ))}
            </div>

            {filteredStyles(category.styles).length === 0 && (
              <div className="text-center py-12">
                <p className="text-neutral-500">
                  No styles found matching your search.
                </p>
              </div>
            )}
          </TabsContent>
        ))}
      </Tabs>

      {/* Help Section */}
      <div className="mt-16 bg-gradient-rajasthani rounded-2xl p-8">
        <div className="text-center max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold text-neutral-900 mb-4">
            Need Custom Design?
          </h2>
          <p className="text-neutral-700 mb-6">
            Don't see what you're looking for? Our expert craftsmen can create
            custom designs based on your specific requirements. Share your ideas
            with us!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="cta-primary"
              onClick={() =>
                window.open("https://wa.me/919876543210", "_blank")
              }
            >
              WhatsApp Custom Request
            </Button>
            <Link to="/tailoring">
              <Button variant="outline">Continue with Tailoring Form</Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Tips Section */}
      <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="text-center p-6 bg-white rounded-xl border border-neutral-200">
          <div className="w-12 h-12 bg-rajasthani-pink/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-rajasthani-pink text-xl">💡</span>
          </div>
          <h3 className="font-semibold text-neutral-900 mb-2">Mix & Match</h3>
          <p className="text-sm text-neutral-600">
            You can combine different styles from various categories to create
            your unique look.
          </p>
        </div>

        <div className="text-center p-6 bg-white rounded-xl border border-neutral-200">
          <div className="w-12 h-12 bg-rajasthani-orange/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-rajasthani-orange text-xl">📏</span>
          </div>
          <h3 className="font-semibold text-neutral-900 mb-2">Size Matters</h3>
          <p className="text-sm text-neutral-600">
            Some styles work better with certain body types. Our experts will
            guide you.
          </p>
        </div>

        <div className="text-center p-6 bg-white rounded-xl border border-neutral-200">
          <div className="w-12 h-12 bg-rajasthani-yellow/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <span className="text-rajasthani-yellow text-xl">✨</span>
          </div>
          <h3 className="font-semibold text-neutral-900 mb-2">Expert Advice</h3>
          <p className="text-sm text-neutral-600">
            Not sure which style to choose? Our designers will recommend the
            best options.
          </p>
        </div>
      </div>
    </div>
  );
}
