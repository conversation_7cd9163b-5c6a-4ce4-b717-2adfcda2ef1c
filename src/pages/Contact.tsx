import { useState } from "react";
import { MapPin, Phone, Mail, Clock, MessageCircle, Send } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function Contact() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    subject: "",
    message: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    // Simulate form submission
    await new Promise((resolve) => setTimeout(resolve, 1000));

    setIsSubmitted(true);
    setIsSubmitting(false);
    setFormData({
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    });
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const contactInfo = [
    {
      icon: MapPin,
      title: "Visit Our Store",
      titleHindi: "हमारे स्टोर पर आएं",
      details: [
        "Bhadra Fort Road, Near Manek Chowk",
        "Bhadra, Ahmedabad, Gujarat 380001",
        "India",
      ],
      color: "text-rajasthani-pink",
    },
    {
      icon: Phone,
      title: "Call Us",
      titleHindi: "हमें कॉल करें",
      details: [
        "+91 98765 43210",
        "+91 79 2550 1234",
        "Toll Free: 1800 123 4567",
      ],
      color: "text-rajasthani-orange",
    },
    {
      icon: Mail,
      title: "Email Us",
      titleHindi: "हमें ईमेल करें",
      details: [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
      ],
      color: "text-rajasthani-yellow",
    },
    {
      icon: Clock,
      title: "Business Hours",
      titleHindi: "व्यापारिक समय",
      details: [
        "Mon - Sat: 10:00 AM - 8:00 PM",
        "Sunday: 11:00 AM - 7:00 PM",
        "Holidays: 12:00 PM - 6:00 PM",
      ],
      color: "text-rajasthani-gold",
    },
  ];

  const departments = [
    {
      name: "General Inquiry",
      nameHindi: "सामान्य पूछताछ",
      description: "Questions about products, services, or general information",
      email: "<EMAIL>",
      phone: "+91 98765 43210",
    },
    {
      name: "Custom Tailoring",
      nameHindi: "कस्टम सिलाई",
      description: "Custom orders, measurements, and tailoring inquiries",
      email: "<EMAIL>",
      phone: "+91 98765 43211",
    },
    {
      name: "Order Support",
      nameHindi: "ऑर्डर सहायता",
      description: "Order status, shipping, returns, and exchanges",
      email: "<EMAIL>",
      phone: "+91 98765 43212",
    },
    {
      name: "Wholesale Inquiry",
      nameHindi: "थोक पूछताछ",
      description: "Bulk orders, wholesale pricing, and business partnerships",
      email: "<EMAIL>",
      phone: "+91 98765 43213",
    },
  ];

  const faqs = [
    {
      question: "What is your return policy?",
      questionHindi: "आपकी वापसी नीति क्या है?",
      answer:
        "We offer 7-day hassle-free returns for all products. Items must be in original condition.",
      answerHindi:
        "हम सभी उत्पादों के लिए 7-दिन की परेशानी मुक्त वापसी प्रदान करते हैं। वस्तुओं को मूल स्थिति में होना चाहिए।",
    },
    {
      question: "Do you offer Cash on Delivery?",
      questionHindi: "क्या आप कैश ऑन डिलीवरी प्रदान करते हैं?",
      answer:
        "Yes, COD is available across India. Additional charges may apply for remote locations.",
      answerHindi:
        "हाँ, पूरे भारत में COD उपलब्ध है। दूरदराज के स्थानों के लिए अतिरिक्त शुल्क लग सकता है।",
    },
    {
      question: "How long does custom tailoring take?",
      questionHindi: "कस्टम सिलाई में कितना समय लगता है?",
      answer:
        "Custom tailoring typically takes 10-20 days depending on the complexity and current orders.",
      answerHindi:
        "कस्टम सिलाई में आमतौर पर 10-20 दिन लगते हैं, जो जटिलता और वर्तमान ऑर्डर पर निर्भर करता है।",
    },
    {
      question: "What are your shipping charges?",
      questionHindi: "आपके शिपिंग शुल्क क्या हैं?",
      answer:
        "Free shipping on orders above ₹1,999. Express delivery available for ₹99.",
      answerHindi:
        "₹1,999 से अधिक के ऑर्डर पर मुफ्त शिपिंग। ₹99 में एक्सप्रेस डिलीवरी उपलब्ध।",
    },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Hero Section */}
      <section className="py-16 bg-gradient-rajasthani">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="heading-decorative text-4xl sm:text-5xl font-bold text-neutral-900 mb-4">
            {t("footer.contact")}
          </h1>
          <p className="text-xl text-hindi text-rajasthani-pink mb-6">
            संपर्क करें
          </p>
          <p className="text-lg text-neutral-700 max-w-2xl mx-auto">
            We're here to help you with any questions about our products,
            services, or custom tailoring. Reach out to us and we'll respond as
            soon as possible.
          </p>
          <p className="text-lg text-neutral-700 text-hindi max-w-2xl mx-auto mt-4">
            हम आपकी किसी भी सवाल में मदद के लिए यहाँ हैं - चाहे वो हमारे
            उत्पादों, सेवाओं या कस्टम सिलाई के बारे में हो। हमसे संपर्क करें और
            हम जल्द से जल्द जवाब देंगे।
          </p>
        </div>
      </section>

      {/* Contact Info Cards */}
      <section className="py-16 -mt-8">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {contactInfo.map((info, index) => {
              const Icon = info.icon;
              return (
                <Card
                  key={index}
                  className="text-center hover:shadow-lg transition-shadow"
                >
                  <CardHeader>
                    <div
                      className={`w-16 h-16 ${info.color.replace("text-", "bg-")}/10 rounded-full flex items-center justify-center mx-auto mb-4`}
                    >
                      <Icon className={`h-8 w-8 ${info.color}`} />
                    </div>
                    <CardTitle className="text-lg">{info.title}</CardTitle>
                    <CardDescription className="text-hindi text-rajasthani-pink">
                      {info.titleHindi}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-1">
                      {info.details.map((detail, detailIndex) => (
                        <p
                          key={detailIndex}
                          className="text-sm text-neutral-600"
                        >
                          {detail}
                        </p>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <div>
              <h2 className="text-3xl font-bold text-neutral-900 mb-2">
                Send us a Message
              </h2>
              <p className="text-lg text-hindi text-rajasthani-pink mb-6">
                हमें संदेश भेजें
              </p>

              {isSubmitted && (
                <Alert className="mb-6 border-green-200 bg-green-50">
                  <AlertDescription className="text-green-800">
                    Thank you for your message! We'll get back to you within 24
                    hours.
                  </AlertDescription>
                </Alert>
              )}

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">{t("form.name")} *</Label>
                    <Input
                      id="name"
                      name="name"
                      type="text"
                      required
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder="Enter your full name"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone">{t("form.phone")}</Label>
                    <Input
                      id="phone"
                      name="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={handleInputChange}
                      placeholder="+91 XXXXX XXXXX"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="email">{t("form.email")} *</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    required
                    value={formData.email}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <Label htmlFor="subject">Subject *</Label>
                  <Input
                    id="subject"
                    name="subject"
                    type="text"
                    required
                    value={formData.subject}
                    onChange={handleInputChange}
                    placeholder="What is this regarding?"
                  />
                </div>

                <div>
                  <Label htmlFor="message">{t("form.message")} *</Label>
                  <Textarea
                    id="message"
                    name="message"
                    required
                    value={formData.message}
                    onChange={handleInputChange}
                    placeholder="Tell us how we can help you..."
                    rows={5}
                  />
                </div>

                <Button
                  type="submit"
                  className="cta-primary w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    "Sending..."
                  ) : (
                    <>
                      <Send className="h-4 w-4 mr-2" />
                      {t("form.submit")}
                    </>
                  )}
                </Button>
              </form>
            </div>

            {/* Additional Info */}
            <div className="space-y-8">
              {/* WhatsApp Support */}
              <Card className="border-green-200 bg-green-50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3 text-green-800">
                    <MessageCircle className="h-6 w-6" />
                    WhatsApp Support
                  </CardTitle>
                  <CardDescription className="text-green-700">
                    Get instant help on WhatsApp
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-green-700 mb-4">
                    For quick assistance, chat with us on WhatsApp. Our support
                    team is available 10 AM - 8 PM, Monday to Saturday.
                  </p>
                  <Button
                    className="bg-green-600 hover:bg-green-700 text-white w-full"
                    onClick={() =>
                      window.open("https://wa.me/919876543210", "_blank")
                    }
                  >
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Chat on WhatsApp
                  </Button>
                </CardContent>
              </Card>

              {/* Departments */}
              <div>
                <h3 className="text-xl font-bold text-neutral-900 mb-4">
                  Contact Departments
                </h3>
                <div className="space-y-4">
                  {departments.map((dept, index) => (
                    <Card key={index}>
                      <CardContent className="p-4">
                        <h4 className="font-semibold text-neutral-900 mb-1">
                          {dept.name}
                        </h4>
                        <p className="text-sm text-hindi text-rajasthani-pink mb-2">
                          {dept.nameHindi}
                        </p>
                        <p className="text-sm text-neutral-600 mb-3">
                          {dept.description}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-2 text-xs">
                          <a
                            href={`mailto:${dept.email}`}
                            className="text-rajasthani-pink hover:underline"
                          >
                            {dept.email}
                          </a>
                          <a
                            href={`tel:${dept.phone}`}
                            className="text-rajasthani-orange hover:underline"
                          >
                            {dept.phone}
                          </a>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-16 bg-white">
        <div className="max-w-4xl mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-neutral-900 mb-2">
              Frequently Asked Questions
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              अक्सर पूछे जाने वाले सवाल
            </p>
          </div>

          <div className="space-y-6">
            {faqs.map((faq, index) => (
              <Card key={index}>
                <CardContent className="p-6">
                  <h3 className="font-semibold text-neutral-900 mb-1">
                    {faq.question}
                  </h3>
                  <p className="text-sm text-hindi text-rajasthani-pink mb-3">
                    {faq.questionHindi}
                  </p>
                  <p className="text-neutral-700 mb-2">{faq.answer}</p>
                  <p className="text-sm text-neutral-600 text-hindi">
                    {faq.answerHindi}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-8">
            <p className="text-neutral-600 mb-4">
              Still have questions? We're here to help!
            </p>
            <Button
              variant="outline"
              onClick={() =>
                window.open("https://wa.me/919876543210", "_blank")
              }
            >
              Contact Support
            </Button>
          </div>
        </div>
      </section>

      {/* Map Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-neutral-900 mb-2">
              Find Our Store
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              हमारा स्टोर खोजें
            </p>
          </div>

          <div className="bg-neutral-200 rounded-xl overflow-hidden">
            <div className="aspect-[16/9] lg:aspect-[21/9] bg-neutral-300 flex items-center justify-center relative">
              <img
                src="https://images.unsplash.com/photo-1519302959554-a75be0afc82a?w=1200&h=600&fit=crop&crop=center"
                alt="Map showing Bayawala Emporium location"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                <div className="text-center text-white">
                  <MapPin className="h-12 w-12 mx-auto mb-4" />
                  <p className="text-lg font-semibold mb-2">Interactive Map</p>
                  <p className="text-sm">
                    Bayawala Emporium, Bhadra Fort Road, Ahmedabad
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
