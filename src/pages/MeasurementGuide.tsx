import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft, Play, Download, Ruler, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useLanguage } from "@/contexts/LanguageContext";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";

export default function MeasurementGuide() {
  const { t } = useLanguage();
  const [activeVideo, setActiveVideo] = useState<string | null>(null);

  const measurementGuides = {
    women: {
      title: "Women's Measurements",
      titleHindi: "महिलाओं के माप",
      measurements: [
        {
          id: "bust",
          name: "Bust/Chest",
          nameHindi: "छाती/बस्ट",
          instruction:
            "Measure around the fullest part of your chest, keeping the tape parallel to the floor.",
          instructionHindi:
            "अपनी छाती के सबसे उभरे हुए हिस्से के चारों ओर नापें, टेप को जमीन के समानांतर रखें।",
          tips: [
            "Wear a well-fitted bra",
            "Keep arms relaxed at sides",
            "Tape should be snug but not tight",
          ],
          image:
            "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=300&h=400&fit=crop&crop=center",
          videoId: "bust-measurement",
        },
        {
          id: "waist",
          name: "Waist",
          nameHindi: "कमर",
          instruction:
            "Measure around your natural waistline, which is typically the narrowest part of your torso.",
          instructionHindi:
            "अपनी प्राकृतिक कमर के चारों ओर नापें, जो आमतौर पर आपके धड़ का सबसे पतला हिस्सा है।",
          tips: [
            "Stand naturally, don't suck in",
            "Find the narrowest point between ribs and hips",
            "Keep one finger between tape and body",
          ],
          image:
            "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300&h=400&fit=crop&crop=center",
          videoId: "waist-measurement",
        },
        {
          id: "hip",
          name: "Hip",
          nameHindi: "कूल्हे",
          instruction:
            "Measure around the fullest part of your hips, usually about 7-9 inches below your waist.",
          instructionHindi:
            "अपने कूल्हों के सबसे चौड़े हिस्से के चारों ओर नापें, आमतौर पर कमर से 7-9 इंच नीचे।",
          tips: [
            "Stand with feet together",
            "Measure over your undergarments",
            "Include buttocks in measurement",
          ],
          image:
            "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=300&h=400&fit=crop&crop=center",
          videoId: "hip-measurement",
        },
        {
          id: "shoulder",
          name: "Shoulder Width",
          nameHindi: "कंधे की चौड़ाई",
          instruction:
            "Measure from the edge of one shoulder to the edge of the other, across the back.",
          instructionHindi:
            "एक कंधे के किनारे से दूसरे कंधे के किनारे तक, पीठ के पार नापें।",
          tips: [
            "Stand straight with arms at sides",
            "Measure across the back",
            "Find the shoulder points where arms meet torso",
          ],
          image:
            "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=300&h=400&fit=crop&crop=center",
          videoId: "shoulder-measurement",
        },
        {
          id: "arm-length",
          name: "Arm Length",
          nameHindi: "बाजू की लंबाई",
          instruction:
            "Measure from shoulder point down to wrist, with arm slightly bent.",
          instructionHindi:
            "कंधे के बिंदु से कलाई तक नापें, बाजू को थोड़ा मोड़कर।",
          tips: [
            "Bend arm slightly at elbow",
            "Measure on the outside of the arm",
            "End at wrist bone",
          ],
          image:
            "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=300&h=400&fit=crop&crop=center",
          videoId: "arm-measurement",
        },
        {
          id: "neck-depth",
          name: "Neck Depth",
          nameHindi: "गले की गहराई",
          instruction:
            "Measure from the center of the neck (base) down to desired neckline depth.",
          instructionHindi:
            "गर्दन के केंद्र (आधार) से वांछित नेकलाइन की गहराई तक नापें।",
          tips: [
            "Mark desired neckline depth first",
            "Measure straight down from neck base",
            "Consider the style you want",
          ],
          image:
            "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=300&h=400&fit=crop&crop=center",
          videoId: "neck-measurement",
        },
      ],
    },
    men: {
      title: "Men's Measurements",
      titleHindi: "पुरुषों के माप",
      measurements: [
        {
          id: "chest",
          name: "Chest",
          nameHindi: "छाती",
          instruction:
            "Measure around the fullest part of your chest, just under the armpits.",
          instructionHindi:
            "अपनी छाती के सबसे चौड़े हिस्से के चारों ओर, बगल के ठीक नीचे नापें।",
          tips: [
            "Keep arms slightly raised",
            "Breathe normally",
            "Tape should be level all around",
          ],
          image:
            "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=400&fit=crop&crop=center",
          videoId: "mens-chest",
        },
        {
          id: "waist",
          name: "Waist",
          nameHindi: "कमर",
          instruction:
            "Measure around your waist at the level where you normally wear your pants.",
          instructionHindi:
            "अपनी कमर के चारों ओर उस स्थान पर नापें जहाँ आप सामान्यतः पैंट पहनते हैं।",
          tips: [
            "Don't hold your breath",
            "Measure over undergarments",
            "Use natural waistline",
          ],
          image:
            "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=400&fit=crop&crop=center",
          videoId: "mens-waist",
        },
        {
          id: "shoulder",
          name: "Shoulder Width",
          nameHindi: "कंधे की चौड़ाई",
          instruction:
            "Measure across the back from shoulder point to shoulder point.",
          instructionHindi:
            "पीठ के पार एक कंधे के बिंदु से दूसरे कंधे के बिंदु तक नापें।",
          tips: [
            "Stand straight and relaxed",
            "Measure across the back",
            "Find where arm meets shoulder",
          ],
          image:
            "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=300&h=400&fit=crop&crop=center",
          videoId: "mens-shoulder",
        },
        {
          id: "kurta-length",
          name: "Kurta Length",
          nameHindi: "कुर्ता की लंबाई",
          instruction:
            "Measure from the base of neck down to desired kurta length.",
          instructionHindi: "गर्दन के आधार से वांछित कुर्ता लंबाई तक नापें।",
          tips: [
            "Decide on desired length first",
            "Measure straight down the front",
            "Consider your height",
          ],
          image:
            "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=300&h=400&fit=crop&crop=center",
          videoId: "kurta-length",
        },
      ],
    },
  };

  const generalTips = [
    {
      title: "Use the Right Tools",
      titleHindi: "सही औजार का उपयोग करें",
      description: "Use a flexible measuring tape, not a ruler or stiff tape.",
      descriptionHindi:
        "लचीले मापने वाले टेप का उपयोग करें, रूलर या कड़े टेप का नहीं।",
    },
    {
      title: "Get Help",
      titleHindi: "सहायता लें",
      description:
        "Have someone help you measure for the most accurate results.",
      descriptionHindi: "सबसे सटीक परिणाम के लिए किसी से मापने में मदद लें।",
    },
    {
      title: "Wear Proper Undergarments",
      titleHindi: "उचित अंडरगारमेंट्स पहनें",
      description:
        "Wear well-fitted undergarments that you'll wear with the garment.",
      descriptionHindi:
        "उचित फिटिंग के अंडरगारमेंट्स पहनें जो आप कपड़े के साथ पहनेंगे।",
    },
    {
      title: "Measure Twice",
      titleHindi: "दो बार नापें",
      description: "Take each measurement twice to ensure accuracy.",
      descriptionHindi: "सटीकता सुनिश्चित करने के लिए हर माप दो बार लें।",
    },
    {
      title: "Stand Naturally",
      titleHindi: "स्वाभाविक रूप से खड़े हों",
      description: "Stand in a natural, relaxed position while measuring.",
      descriptionHindi: "नापते समय प्राकृतिक, आराम की स्थिति में खड़े हों।",
    },
    {
      title: "Record Everything",
      titleHindi: "सब कुछ रिकॉर्ड करें",
      description: "Write down all measurements immediately after taking them.",
      descriptionHindi: "सभी माप लेने के तुरंत बाद लिख लें।",
    },
  ];

  const commonMistakes = [
    {
      mistake: "Pulling the tape too tight",
      mistakeHindi: "टेप को बहुत कसकर खींचना",
      solution: "Keep the tape snug but comfortable",
      solutionHindi: "टेप को कसा रखें लेकिन आरामदायक",
    },
    {
      mistake: "Measuring over thick clothing",
      mistakeHindi: "मोटे कपड़ों पर नापना",
      solution: "Measure over thin, fitted undergarments only",
      solutionHindi: "केवल पतले, फिटेड अंडरगारमेंट्स पर नापें",
    },
    {
      mistake: "Not standing straight",
      mistakeHindi: "सीधे न खड़े होना",
      solution: "Maintain good posture throughout",
      solutionHindi: "पूरे समय अच्छी मुद्रा बनाए रखें",
    },
    {
      mistake: "Measuring at wrong body points",
      mistakeHindi: "गलत शरीर के बिंदुओं पर नापना",
      solution: "Follow the guide carefully for each measurement",
      solutionHindi: "हर माप के लिए गाइड का सावधानीपूर्वक पालन करें",
    },
  ];

  return (
    <div className="max-w-6xl mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <Link
          to="/tailoring"
          className="inline-flex items-center text-rajasthani-pink hover:underline mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Tailoring Form
        </Link>

        <h1 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
          {t("tailoring.guide")}
        </h1>
        <p className="text-lg text-neutral-600 max-w-3xl">
          माप गाइड - Learn how to take accurate measurements for the perfect
          fit. Follow our step-by-step instructions with diagrams and video
          tutorials.
        </p>
      </div>

      {/* Important Notice */}
      <Alert className="mb-8 border-rajasthani-orange bg-rajasthani-orange/5">
        <AlertCircle className="h-4 w-4 text-rajasthani-orange" />
        <AlertDescription className="text-neutral-700">
          <strong>Important:</strong> Accurate measurements are crucial for a
          perfect fit. Take your time and follow each step carefully. When in
          doubt, contact our experts for guidance.
        </AlertDescription>
      </Alert>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
        <Button
          variant="outline"
          className="flex items-center gap-2 h-auto p-4"
          onClick={() => window.open("https://wa.me/************", "_blank")}
        >
          <span className="text-2xl">📞</span>
          <div className="text-left">
            <div className="font-semibold">Get Help</div>
            <div className="text-xs text-neutral-600">Call our experts</div>
          </div>
        </Button>

        <Button
          variant="outline"
          className="flex items-center gap-2 h-auto p-4"
        >
          <Download className="h-6 w-6" />
          <div className="text-left">
            <div className="font-semibold">Download PDF</div>
            <div className="text-xs text-neutral-600">Printable guide</div>
          </div>
        </Button>

        <Button
          variant="outline"
          className="flex items-center gap-2 h-auto p-4"
        >
          <Play className="h-6 w-6" />
          <div className="text-left">
            <div className="font-semibold">Video Tutorials</div>
            <div className="text-xs text-neutral-600">Step-by-step videos</div>
          </div>
        </Button>
      </div>

      {/* Measurement Guides */}
      <Tabs defaultValue="women" className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-8">
          <TabsTrigger value="women">
            Women's Guide
            <span className="text-xs text-hindi ml-2">(महिला गाइड)</span>
          </TabsTrigger>
          <TabsTrigger value="men">
            Men's Guide
            <span className="text-xs text-hindi ml-2">(पुरुष गाइड)</span>
          </TabsTrigger>
        </TabsList>

        {Object.entries(measurementGuides).map(([key, guide]) => (
          <TabsContent key={key} value={key} className="space-y-8">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-bold text-neutral-900 mb-2">
                {guide.title}
              </h2>
              <p className="text-lg text-hindi text-rajasthani-pink">
                {guide.titleHindi}
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {guide.measurements.map((measurement, index) => (
                <Card key={measurement.id} className="overflow-hidden">
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div>
                        <CardTitle className="text-lg flex items-center gap-2">
                          <span className="w-8 h-8 bg-rajasthani-pink text-white rounded-full flex items-center justify-center text-sm font-bold">
                            {index + 1}
                          </span>
                          {measurement.name}
                        </CardTitle>
                        <CardDescription className="text-hindi text-rajasthani-pink mt-1">
                          {measurement.nameHindi}
                        </CardDescription>
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setActiveVideo(measurement.videoId)}
                      >
                        <Play className="h-4 w-4 mr-1" />
                        Video
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="aspect-[3/4] bg-neutral-100 rounded-lg overflow-hidden">
                      <img
                        src={measurement.image}
                        alt={`How to measure ${measurement.name}`}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    <div>
                      <h4 className="font-semibold text-neutral-900 mb-2">
                        Instructions:
                      </h4>
                      <p className="text-sm text-neutral-700 mb-2">
                        {measurement.instruction}
                      </p>
                      <p className="text-sm text-neutral-600 text-hindi">
                        {measurement.instructionHindi}
                      </p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-neutral-900 mb-2">
                        Tips:
                      </h4>
                      <ul className="text-sm text-neutral-600 space-y-1">
                        {measurement.tips.map((tip, tipIndex) => (
                          <li key={tipIndex} className="flex items-start gap-2">
                            <div className="w-1.5 h-1.5 bg-rajasthani-pink rounded-full mt-2 flex-shrink-0" />
                            {tip}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>
        ))}
      </Tabs>

      {/* General Tips */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-neutral-900 mb-8 text-center">
          General Measurement Tips
          <span className="block text-lg text-hindi text-rajasthani-pink mt-1">
            सामान्य मापने की युक्तियां
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {generalTips.map((tip, index) => (
            <Card key={index} className="text-center">
              <CardContent className="p-6">
                <div className="w-12 h-12 bg-rajasthani-pink/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Ruler className="h-6 w-6 text-rajasthani-pink" />
                </div>
                <h3 className="font-semibold text-neutral-900 mb-2">
                  {tip.title}
                </h3>
                <p className="text-sm text-hindi text-rajasthani-pink mb-2">
                  {tip.titleHindi}
                </p>
                <p className="text-sm text-neutral-600 mb-2">
                  {tip.description}
                </p>
                <p className="text-xs text-neutral-500 text-hindi">
                  {tip.descriptionHindi}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Common Mistakes */}
      <div className="mt-16">
        <h2 className="text-2xl font-bold text-neutral-900 mb-8 text-center">
          Common Mistakes to Avoid
          <span className="block text-lg text-hindi text-rajasthani-pink mt-1">
            बचने योग्य सामान्य गलतियां
          </span>
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {commonMistakes.map((item, index) => (
            <Card key={index} className="border-l-4 border-l-red-500">
              <CardContent className="p-6">
                <div className="space-y-3">
                  <div>
                    <h3 className="font-semibold text-red-600 flex items-center gap-2">
                      ❌ {item.mistake}
                    </h3>
                    <p className="text-sm text-red-500 text-hindi">
                      {item.mistakeHindi}
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold text-green-600 flex items-center gap-2">
                      ✅ {item.solution}
                    </h4>
                    <p className="text-sm text-green-600 text-hindi">
                      {item.solutionHindi}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Help Section */}
      <div className="mt-16 bg-gradient-rajasthani rounded-2xl p-8">
        <div className="text-center max-w-2xl mx-auto">
          <h2 className="text-2xl font-bold text-neutral-900 mb-4">
            Still Need Help?
          </h2>
          <p className="text-neutral-700 mb-6">
            Our measurement experts are here to help you get the perfect fit.
            Contact us for personalized assistance or to schedule a video call
            for guided measuring.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              className="cta-primary"
              onClick={() =>
                window.open("https://wa.me/************", "_blank")
              }
            >
              WhatsApp Support
            </Button>
            <Button variant="outline">Schedule Video Call</Button>
            <Link to="/tailoring">
              <Button variant="outline">Back to Tailoring Form</Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
