import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  ChevronRight,
  Star,
  ShoppingBag,
  Users,
  Award,
  Sparkles,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/ui/product-card";
import { TrustBadges } from "@/components/ui/trust-badges";
import { useLanguage } from "@/contexts/LanguageContext";

export default function Index() {
  const { t } = useLanguage();
  const [currentBanner, setCurrentBanner] = useState(0);

  // Real online image sources for banners
  const banners = [
    {
      id: 1,
      image:
        "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=800&h=400&fit=crop&crop=center",
      title: t("hero.title"),
      subtitle: t("hero.subtitle"),
      cta: t("hero.cta"),
      link: "/category/rajputi",
    },
    {
      id: 2,
      image:
        "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=800&h=400&fit=crop&crop=center",
      title: "Festive Collection 2024",
      subtitle: "Celebrate with traditional elegance",
      cta: "Shop Festive",
      link: "/category/festive",
    },
    {
      id: 3,
      image:
        "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=800&h=400&fit=crop&crop=center",
      title: "Custom Tailoring Now Available",
      subtitle: "Get your perfect fit delivered to your door",
      cta: "Start Tailoring",
      link: "/tailoring",
    },
  ];

  const categories = [
    {
      id: "rajputi",
      name: t("category.rajputi.title"),
      image:
        "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=200&fit=crop&crop=center",
      href: "/category/rajputi",
      color: "bg-rajasthani-pink",
    },
    {
      id: "comforters",
      name: t("category.comforters.title"),
      image:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop&crop=center",
      href: "/category/comforters",
      color: "bg-rajasthani-orange",
    },
    {
      id: "sarees",
      name: t("category.sarees.title"),
      image:
        "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=200&fit=crop&crop=center",
      href: "/category/sarees",
      color: "bg-rajasthani-yellow",
    },
    {
      id: "suits",
      name: t("category.suits.title"),
      image:
        "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=200&fit=crop&crop=center",
      href: "/category/suits",
      color: "bg-rajasthani-gold",
    },
    {
      id: "linens",
      name: t("category.linens.title"),
      image:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop&crop=center",
      href: "/category/linens",
      color: "bg-neutral-400",
    },
  ];

  const featuredProducts = [
    {
      id: "1",
      title: "Royal Blue Rajputi Poshak with Gota Work",
      price: 12999,
      originalPrice: 15999,
      image:
        "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300&h=400&fit=crop&crop=center",
      rating: 4.8,
      reviewCount: 124,
      discount: 19,
      isCODAvailable: true,
      isFreeShipping: true,
    },
    {
      id: "2",
      title: "Handwoven Bandhani Saree - Pink & Gold",
      price: 8999,
      originalPrice: 11999,
      image:
        "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=300&h=400&fit=crop&crop=center",
      rating: 4.7,
      reviewCount: 89,
      discount: 25,
      isCODAvailable: true,
      isFreeShipping: true,
    },
    {
      id: "3",
      title: "Rajasthani Comforter Set - Double Bed",
      price: 3999,
      originalPrice: 5999,
      image:
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=400&fit=crop&crop=center",
      rating: 4.6,
      reviewCount: 156,
      discount: 33,
      isCODAvailable: true,
      isFreeShipping: true,
    },
    {
      id: "4",
      title: "Embroidered Kurta Set with Palazzo",
      price: 4999,
      originalPrice: 6999,
      image:
        "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=300&h=400&fit=crop&crop=center",
      rating: 4.9,
      reviewCount: 67,
      discount: 29,
      isCODAvailable: true,
      isFreeShipping: true,
    },
  ];

  const testimonials = [
    {
      id: 1,
      name: "Priya Sharma",
      location: "Mumbai",
      text: "Beautiful quality and authentic Rajasthani work. The lehenga I ordered was exactly as shown!",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=60&h=60&fit=crop&crop=face",
    },
    {
      id: 2,
      name: "Anita Patel",
      location: "Delhi",
      text: "Great collection and fast delivery. Love the traditional designs with modern fitting.",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1548142813-c348350df52b?w=60&h=60&fit=crop&crop=face",
    },
    {
      id: 3,
      name: "Meera Singh",
      location: "Jaipur",
      text: "Excellent customer service and genuine Rajasthani textiles. Highly recommended!",
      rating: 5,
      image:
        "https://images.unsplash.com/photo-1547425260-76bcadfb4f2c?w=60&h=60&fit=crop&crop=face",
    },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Hero Banner */}
      <section className="relative">
        <div className="relative h-[60vh] sm:h-[70vh] overflow-hidden">
          {banners.map((banner, index) => (
            <div
              key={banner.id}
              className={`absolute inset-0 transition-opacity duration-500 ${
                index === currentBanner ? "opacity-100" : "opacity-0"
              }`}
            >
              <img
                src={banner.image}
                alt={banner.title}
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-black/30" />
              <div className="absolute inset-0 flex items-center justify-center text-center">
                <div className="max-w-2xl px-4">
                  <h1 className="heading-decorative text-2xl sm:text-4xl lg:text-5xl font-bold text-white mb-4">
                    {banner.title}
                  </h1>
                  <p className="text-lg sm:text-xl text-white/90 mb-8">
                    {banner.subtitle}
                  </p>
                  <Link to={banner.link}>
                    <Button className="cta-primary text-lg px-8 py-3">
                      {banner.cta}
                      <ChevronRight className="ml-2 h-5 w-5" />
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Banner indicators */}
        <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
          {banners.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentBanner(index)}
              className={`w-3 h-3 rounded-full transition-colors ${
                index === currentBanner ? "bg-white" : "bg-white/50"
              }`}
            />
          ))}
        </div>
      </section>

      {/* Welcome Message */}
      <section className="py-8 bg-gradient-rajasthani">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h2 className="text-xl sm:text-2xl font-semibold text-neutral-800 mb-2">
            {t("welcome.text")}
          </h2>
          <div className="w-24 h-1 bg-rajasthani-pink mx-auto rounded-full" />
        </div>
      </section>

      {/* Trust Badges */}
      <section className="py-6 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <TrustBadges variant="horizontal" />
        </div>
      </section>

      {/* Quick Categories */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-2">
              Shop by Category
            </h2>
            <p className="text-neutral-600">
              Explore our authentic Rajasthani collections
            </p>
          </div>

          <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-4">
            {categories.map((category) => (
              <Link
                key={category.id}
                to={category.href}
                className="category-card group"
              >
                <div className="aspect-square relative overflow-hidden">
                  <img
                    src={category.image}
                    alt={category.name}
                    className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute bottom-0 left-0 right-0 p-3">
                    <h3 className="font-semibold text-white text-sm sm:text-base text-center">
                      {category.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Products */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-2">
                Featured Collection
              </h2>
              <p className="text-neutral-600">
                Handpicked bestsellers from our artisans
              </p>
            </div>
            <Link to="/products">
              <Button variant="outline" className="hidden sm:flex">
                View All
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {featuredProducts.map((product) => (
              <ProductCard
                key={product.id}
                {...product}
                onAddToCart={(id) => console.log("Add to cart:", id)}
                onToggleWishlist={(id) => console.log("Toggle wishlist:", id)}
              />
            ))}
          </div>

          <div className="text-center mt-8 sm:hidden">
            <Link to="/products">
              <Button variant="outline">
                View All Products
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Promotional Banner */}
      <section className="py-12 bg-rajasthani-pink/10">
        <div className="max-w-7xl mx-auto px-4">
          <div className="bg-white rounded-2xl p-8 shadow-lg rajasthani-pattern">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-rajasthani-pink rounded-full mb-4">
                <Sparkles className="h-8 w-8 text-white" />
              </div>
              <h2 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-4">
                Custom Tailoring Service
              </h2>
              <p className="text-neutral-600 mb-6 max-w-2xl mx-auto">
                Get your perfect-fit Rajputi Poshak, Lehenga, or Suit tailored
                by our expert craftsmen. Submit your measurements and choose
                from our exclusive design catalog.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/tailoring">
                  <Button className="cta-primary">Start Custom Order</Button>
                </Link>
                <Link to="/measurement-guide">
                  <Button variant="outline">View Measurement Guide</Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Social Proof & Stats */}
      <section className="py-12">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-rajasthani-pink/10 rounded-full mb-3">
                <Users className="h-6 w-6 text-rajasthani-pink" />
              </div>
              <div className="text-2xl font-bold text-neutral-900">50,000+</div>
              <div className="text-sm text-neutral-600">Happy Customers</div>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-rajasthani-orange/10 rounded-full mb-3">
                <Star className="h-6 w-6 text-rajasthani-orange" />
              </div>
              <div className="text-2xl font-bold text-neutral-900">4.8/5</div>
              <div className="text-sm text-neutral-600">Average Rating</div>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-rajasthani-yellow/10 rounded-full mb-3">
                <ShoppingBag className="h-6 w-6 text-rajasthani-yellow" />
              </div>
              <div className="text-2xl font-bold text-neutral-900">10,000+</div>
              <div className="text-sm text-neutral-600">Products Sold</div>
            </div>
            <div className="text-center">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-rajasthani-gold/10 rounded-full mb-3">
                <Award className="h-6 w-6 text-rajasthani-gold" />
              </div>
              <div className="text-2xl font-bold text-neutral-900">
                49 Years
              </div>
              <div className="text-sm text-neutral-600">of Heritage</div>
            </div>
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-12 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-8">
            <h2 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-2">
              What Our Customers Say
            </h2>
            <p className="text-neutral-600">
              Genuine reviews from our valued customers
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="bg-neutral-50 rounded-xl p-6 hover:shadow-lg transition-shadow"
              >
                <div className="flex items-center gap-3 mb-4">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-12 h-12 rounded-full object-cover"
                  />
                  <div>
                    <h4 className="font-semibold text-neutral-900">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-neutral-600">
                      {testimonial.location}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-1 mb-3">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star
                      key={i}
                      className="h-4 w-4 fill-yellow-400 text-yellow-400"
                    />
                  ))}
                </div>
                <p className="text-neutral-700 text-sm leading-relaxed">
                  "{testimonial.text}"
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Newsletter & Instagram */}
      <section className="py-12 bg-gradient-rajasthani">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Newsletter */}
            <div className="text-center lg:text-left">
              <h3 className="heading-decorative text-2xl font-bold text-neutral-900 mb-4">
                Stay Connected
              </h3>
              <p className="text-neutral-600 mb-6">
                Follow us on Instagram for daily style inspiration and be the
                first to know about new collections, exclusive offers, and
                behind-the-scenes content.
              </p>
              <div className="flex flex-col sm:flex-row gap-3">
                <a
                  href="https://instagram.com/bayawalaemporium"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block"
                >
                  <Button className="cta-secondary w-full sm:w-auto">
                    Follow on Instagram
                  </Button>
                </a>
                <Link to="/newsletter">
                  <Button variant="outline" className="w-full sm:w-auto">
                    Subscribe Newsletter
                  </Button>
                </Link>
              </div>
            </div>

            {/* Instagram Preview */}
            <div className="grid grid-cols-3 gap-2">
              {[
                "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=200&h=200&fit=crop&crop=center",
                "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=200&h=200&fit=crop&crop=center",
                "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=200&h=200&fit=crop&crop=center",
                "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=200&h=200&fit=crop&crop=center",
                "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=200&h=200&fit=crop&crop=center",
                "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=200&h=200&fit=crop&crop=center",
              ].map((src, i) => (
                <div
                  key={i}
                  className="aspect-square rounded-lg overflow-hidden"
                >
                  <img
                    src={src}
                    alt={`Instagram post ${i + 1}`}
                    className="w-full h-full object-cover hover:scale-110 transition-transform duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
