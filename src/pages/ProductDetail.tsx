import { useState } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import {
  Star,
  Heart,
  Share2,
  ShoppingCart,
  Truck,
  RotateCcw,
  Shield,
  Minus,
  Plus,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/ui/product-card";
import { useLanguage } from "@/contexts/LanguageContext";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";

export default function ProductDetail() {
  const { id } = useParams<{ id: string }>();
  const { t } = useLanguage();
  const [selectedImage, setSelectedImage] = useState(0);
  const [selectedSize, setSelectedSize] = useState("");
  const [selectedColor, setSelectedColor] = useState("");
  const [quantity, setQuantity] = useState(1);
  const [isWishlisted, setIsWishlisted] = useState(false);

  // Mock product data with real images
  const product = {
    id: id || "1",
    title: "Royal Blue Rajputi Poshak with Golden Gota Work",
    price: 12999,
    originalPrice: 15999,
    discount: 19,
    rating: 4.8,
    reviewCount: 124,
    images: [
      "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=600&h=800&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=600&h=800&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=600&h=800&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=600&h=800&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=600&h=800&fit=crop&crop=center",
    ],
    colors: [
      { name: "Royal Blue", value: "#1E40AF", available: true },
      { name: "Deep Pink", value: "#E62A81", available: true },
      { name: "Golden Yellow", value: "#F0E245", available: false },
      { name: "Emerald Green", value: "#059669", available: true },
    ],
    sizes: ["XS", "S", "M", "L", "XL", "XXL"],
    description:
      "Exquisite Royal Blue Rajputi Poshak featuring intricate golden gota work and traditional mirror embroidery. This stunning piece is perfect for weddings, festivals, and special occasions. Crafted by skilled artisans in Rajasthan using authentic techniques.",
    features: [
      "Handcrafted in Rajasthan",
      "Traditional gota work and mirror embroidery",
      "Premium quality fabric",
      "Comfortable fit with traditional styling",
      "Perfect for weddings and festivals",
      "Available in multiple sizes and colors",
    ],
    specifications: {
      Fabric: "Premium Cotton Silk",
      Work: "Hand Embroidered Gota and Mirror Work",
      Occasion: "Wedding, Festival, Party",
      Care: "Dry Clean Only",
      Origin: "Rajasthan, India",
      Delivery: "7-10 Business Days",
    },
    isCODAvailable: true,
    isFreeShipping: true,
    inStock: true,
    stockCount: 8,
  };

  const reviews = [
    {
      id: 1,
      user: "Priya S.",
      rating: 5,
      date: "2024-01-15",
      review:
        "Absolutely beautiful! The quality is amazing and the embroidery work is exquisite. Fits perfectly and looks exactly like the photos.",
      images: [
        "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=100&h=100&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=100&h=100&fit=crop&crop=center",
      ],
    },
    {
      id: 2,
      user: "Anita P.",
      rating: 5,
      date: "2024-01-10",
      review:
        "Love this poshak! The color is vibrant and the fabric quality is excellent. Great for special occasions.",
      images: [],
    },
    {
      id: 3,
      user: "Meera K.",
      rating: 4,
      date: "2024-01-05",
      review:
        "Very nice product. The gota work is beautiful. Delivery was on time. Highly recommended!",
      images: [
        "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=100&h=100&fit=crop&crop=center",
      ],
    },
  ];

  const relatedProducts = Array.from({ length: 4 }, (_, i) => ({
    id: `related-${i + 1}`,
    title: `Similar Rajputi Poshak ${i + 1}`,
    price: Math.floor(Math.random() * 20000) + 8000,
    originalPrice: Math.floor(Math.random() * 25000) + 10000,
    image: [
      "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300&h=400&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=300&h=400&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=300&h=400&fit=crop&crop=center",
      "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=300&h=400&fit=crop&crop=center",
    ][i],
    rating: 4.2 + Math.random() * 0.8,
    reviewCount: Math.floor(Math.random() * 100) + 20,
    discount: Math.floor(Math.random() * 30) + 10,
    isCODAvailable: true,
    isFreeShipping: true,
  }));

  const nextImage = () => {
    setSelectedImage((prev) => (prev + 1) % product.images.length);
  };

  const prevImage = () => {
    setSelectedImage(
      (prev) => (prev - 1 + product.images.length) % product.images.length,
    );
  };

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* Breadcrumb */}
      <nav className="flex items-center gap-2 text-sm text-neutral-600 mb-6">
        <Link to="/" className="hover:text-rajasthani-pink">
          Home
        </Link>
        <span>•</span>
        <Link to="/category/rajputi" className="hover:text-rajasthani-pink">
          Rajputi Dresses
        </Link>
        <span>•</span>
        <span className="text-neutral-900 font-medium truncate">
          {product.title}
        </span>
      </nav>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
        {/* Product Images */}
        <div className="space-y-4">
          <div className="relative">
            <div className="aspect-[3/4] rounded-lg overflow-hidden bg-neutral-100">
              <img
                src={product.images[selectedImage]}
                alt={product.title}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Image Navigation */}
            <button
              onClick={prevImage}
              className="absolute left-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white rounded-full shadow-lg transition-colors"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <button
              onClick={nextImage}
              className="absolute right-2 top-1/2 -translate-y-1/2 p-2 bg-white/80 hover:bg-white rounded-full shadow-lg transition-colors"
            >
              <ChevronRight className="h-5 w-5" />
            </button>

            {/* Badges */}
            <div className="absolute top-4 left-4 flex flex-col gap-2">
              <Badge className="bg-rajasthani-pink text-white">
                {product.discount}% OFF
              </Badge>
              {product.isFreeShipping && (
                <Badge className="bg-green-600 text-white">
                  {t("trust.freeshipping")}
                </Badge>
              )}
            </div>
          </div>

          {/* Thumbnail Images */}
          <div className="flex gap-2 overflow-x-auto">
            {product.images.map((image, index) => (
              <button
                key={index}
                onClick={() => setSelectedImage(index)}
                className={`flex-shrink-0 w-16 h-20 rounded-lg overflow-hidden border-2 transition-colors ${
                  selectedImage === index
                    ? "border-rajasthani-pink"
                    : "border-neutral-200"
                }`}
              >
                <img
                  src={image}
                  alt={`Product view ${index + 1}`}
                  className="w-full h-full object-cover"
                />
              </button>
            ))}
          </div>
        </div>

        {/* Product Info */}
        <div className="space-y-6">
          <div>
            <h1 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-2">
              {product.title}
            </h1>

            {/* Rating */}
            <div className="flex items-center gap-2 mb-4">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`h-4 w-4 ${
                      i < Math.floor(product.rating)
                        ? "fill-yellow-400 text-yellow-400"
                        : "text-neutral-300"
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-neutral-600">
                {product.rating} ({product.reviewCount} reviews)
              </span>
            </div>

            {/* Price */}
            <div className="flex items-center gap-3 mb-6">
              <span className="text-3xl font-bold text-neutral-900">
                ₹{product.price.toLocaleString()}
              </span>
              <span className="text-xl text-neutral-500 line-through">
                ₹{product.originalPrice.toLocaleString()}
              </span>
              <Badge className="bg-green-100 text-green-800">
                Save ₹{(product.originalPrice - product.price).toLocaleString()}
              </Badge>
            </div>
          </div>

          {/* Color Selection */}
          <div>
            <h3 className="font-semibold mb-3">
              Color: {selectedColor || "Select Color"}
            </h3>
            <div className="flex gap-2">
              {product.colors.map((color) => (
                <button
                  key={color.name}
                  onClick={() =>
                    color.available && setSelectedColor(color.name)
                  }
                  disabled={!color.available}
                  className={`w-10 h-10 rounded-full border-2 transition-all ${
                    selectedColor === color.name
                      ? "border-rajasthani-pink scale-110"
                      : "border-neutral-300"
                  } ${!color.available ? "opacity-50 cursor-not-allowed" : "hover:scale-110"}`}
                  style={{ backgroundColor: color.value }}
                  title={color.name}
                />
              ))}
            </div>
          </div>

          {/* Size Selection */}
          <div>
            <h3 className="font-semibold mb-3">
              Size: {selectedSize || "Select Size"}
            </h3>
            <div className="flex flex-wrap gap-2">
              {product.sizes.map((size) => (
                <button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  className={`px-4 py-2 border rounded-lg font-medium transition-colors ${
                    selectedSize === size
                      ? "border-rajasthani-pink bg-rajasthani-pink text-white"
                      : "border-neutral-300 hover:border-rajasthani-pink"
                  }`}
                >
                  {size}
                </button>
              ))}
            </div>
            <Link
              to="/measurement-guide"
              className="text-sm text-rajasthani-pink hover:underline mt-2 inline-block"
            >
              View Size Guide
            </Link>
          </div>

          {/* Quantity */}
          <div>
            <h3 className="font-semibold mb-3">Quantity</h3>
            <div className="flex items-center gap-3">
              <div className="flex items-center border border-neutral-300 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="p-2 hover:bg-neutral-100 transition-colors"
                  disabled={quantity <= 1}
                >
                  <Minus className="h-4 w-4" />
                </button>
                <span className="px-4 py-2 font-medium">{quantity}</span>
                <button
                  onClick={() =>
                    setQuantity(Math.min(product.stockCount, quantity + 1))
                  }
                  className="p-2 hover:bg-neutral-100 transition-colors"
                  disabled={quantity >= product.stockCount}
                >
                  <Plus className="h-4 w-4" />
                </button>
              </div>
              <span className="text-sm text-neutral-600">
                {product.stockCount} pieces available
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="space-y-4">
            <div className="flex gap-3">
              <Button
                className="cta-primary flex-1"
                disabled={!selectedColor || !selectedSize}
              >
                <ShoppingCart className="h-5 w-5 mr-2" />
                {t("product.addtocart")}
              </Button>
              <Button
                variant="outline"
                onClick={() => setIsWishlisted(!isWishlisted)}
                className={
                  isWishlisted
                    ? "text-rajasthani-pink border-rajasthani-pink"
                    : ""
                }
              >
                <Heart
                  className={`h-5 w-5 ${isWishlisted ? "fill-current" : ""}`}
                />
              </Button>
              <Button variant="outline">
                <Share2 className="h-5 w-5" />
              </Button>
            </div>

            <Button
              variant="outline"
              className="w-full"
              disabled={!selectedColor || !selectedSize}
            >
              {t("product.buynow")}
            </Button>
          </div>

          {/* Features */}
          <div className="grid grid-cols-3 gap-4 p-4 bg-neutral-50 rounded-lg">
            <div className="text-center">
              <Truck className="h-6 w-6 text-rajasthani-pink mx-auto mb-2" />
              <p className="text-xs font-medium">{t("trust.freeshipping")}</p>
            </div>
            <div className="text-center">
              <Shield className="h-6 w-6 text-rajasthani-orange mx-auto mb-2" />
              <p className="text-xs font-medium">{t("trust.cod")}</p>
            </div>
            <div className="text-center">
              <RotateCcw className="h-6 w-6 text-green-600 mx-auto mb-2" />
              <p className="text-xs font-medium">{t("trust.returns")}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Product Details Tabs */}
      <div className="mt-12">
        <Tabs defaultValue="description" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="description">Description</TabsTrigger>
            <TabsTrigger value="specifications">Specifications</TabsTrigger>
            <TabsTrigger value="reviews">
              Reviews ({product.reviewCount})
            </TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-6">
            <div className="prose max-w-none">
              <p className="text-neutral-700 leading-relaxed mb-4">
                {product.description}
              </p>
              <h3 className="font-semibold text-neutral-900 mb-3">
                Key Features:
              </h3>
              <ul className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {product.features.map((feature, index) => (
                  <li
                    key={index}
                    className="flex items-center gap-2 text-neutral-700"
                  >
                    <div className="w-2 h-2 bg-rajasthani-pink rounded-full flex-shrink-0" />
                    {feature}
                  </li>
                ))}
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="specifications" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(product.specifications).map(([key, value]) => (
                <div
                  key={key}
                  className="flex justify-between py-3 border-b border-neutral-200"
                >
                  <span className="font-medium text-neutral-900">{key}</span>
                  <span className="text-neutral-700">{value}</span>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <div className="space-y-6">
              {reviews.map((review) => (
                <div
                  key={review.id}
                  className="border-b border-neutral-200 pb-6"
                >
                  <div className="flex items-center gap-3 mb-3">
                    <div className="w-10 h-10 bg-rajasthani-pink/10 rounded-full flex items-center justify-center">
                      <span className="font-semibold text-rajasthani-pink">
                        {review.user.charAt(0)}
                      </span>
                    </div>
                    <div>
                      <p className="font-medium text-neutral-900">
                        {review.user}
                      </p>
                      <div className="flex items-center gap-2">
                        <div className="flex">
                          {[...Array(review.rating)].map((_, i) => (
                            <Star
                              key={i}
                              className="h-3 w-3 fill-yellow-400 text-yellow-400"
                            />
                          ))}
                        </div>
                        <span className="text-xs text-neutral-600">
                          {review.date}
                        </span>
                      </div>
                    </div>
                  </div>
                  <p className="text-neutral-700 mb-3">{review.review}</p>
                  {review.images.length > 0 && (
                    <div className="flex gap-2">
                      {review.images.map((image, index) => (
                        <img
                          key={index}
                          src={image}
                          alt={`Review image ${index + 1}`}
                          className="w-16 h-16 rounded-lg object-cover"
                        />
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </div>

      {/* Related Products */}
      <div className="mt-12">
        <h2 className="heading-decorative text-2xl font-bold text-neutral-900 mb-6">
          You May Also Like
        </h2>
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
          {relatedProducts.map((product) => (
            <ProductCard
              key={product.id}
              {...product}
              onAddToCart={(id) => console.log("Add to cart:", id)}
              onToggleWishlist={(id) => console.log("Toggle wishlist:", id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
