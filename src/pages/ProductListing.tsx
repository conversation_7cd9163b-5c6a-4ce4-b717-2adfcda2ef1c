import { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import {
  Filter,
  SlidersHorizontal,
  ChevronDown,
  Grid3X3,
  List,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ProductCard } from "@/components/ui/product-card";
import { TrustBadges } from "@/components/ui/trust-badges";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";

export default function ProductListing() {
  const { category } = useParams<{ category: string }>();
  const { t } = useLanguage();
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState("popularity");
  const [priceRange, setPriceRange] = useState([0, 50000]);
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string[]>
  >({});

  // Real product images for different categories
  const getProductImage = (category: string, index: number) => {
    const imageMapping = {
      rajputi: [
        "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1515886657613-9f3515b0c78f?w=300&h=400&fit=crop&crop=center",
      ],
      comforters: [
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1571508601891-ca5e7a713859?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=300&h=400&fit=crop&crop=center",
      ],
      sarees: [
        "https://images.unsplash.com/photo-1624206112918-f140f087f9b5?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1605721911519-3dfeb3be25e7?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1614280092040-d1cf1dcde395?w=300&h=400&fit=crop&crop=center",
      ],
      suits: [
        "https://images.unsplash.com/photo-1564169978-4e8b1de03cff?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1594736797933-d0d6c9de3df2?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1610030469983-98e550d6193c?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1583391733956-6c78276477e1?w=300&h=400&fit=crop&crop=center",
      ],
      linens: [
        "https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1555041469-a586c61ea9bc?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1571508601891-ca5e7a713859?w=300&h=400&fit=crop&crop=center",
        "https://images.unsplash.com/photo-1522771739844-6a9f6d5f14af?w=300&h=400&fit=crop&crop=center",
      ],
    };

    const categoryImages =
      imageMapping[category as keyof typeof imageMapping] ||
      imageMapping.rajputi;
    return categoryImages[index % categoryImages.length];
  };

  // Mock data - replace with real API calls
  const categoryInfo = {
    rajputi: {
      title: t("category.rajputi.title"),
      description: t("category.rajputi.desc"),
      totalProducts: 127,
    },
    comforters: {
      title: t("category.comforters.title"),
      description: t("category.comforters.desc"),
      totalProducts: 89,
    },
    sarees: {
      title: t("category.sarees.title"),
      description: t("category.sarees.desc"),
      totalProducts: 156,
    },
    suits: {
      title: t("category.suits.title"),
      description: t("category.suits.desc"),
      totalProducts: 94,
    },
    linens: {
      title: t("category.linens.title"),
      description: t("category.linens.desc"),
      totalProducts: 78,
    },
  };

  const currentCategory = categoryInfo[category as keyof typeof categoryInfo];

  const products = Array.from({ length: 12 }, (_, i) => ({
    id: `${category}-${i + 1}`,
    title: `${currentCategory?.title || "Product"} ${i + 1}`,
    price: Math.floor(Math.random() * 20000) + 2000,
    originalPrice: Math.floor(Math.random() * 30000) + 3000,
    image: getProductImage(category || "rajputi", i),
    rating: 4.2 + Math.random() * 0.8,
    reviewCount: Math.floor(Math.random() * 200) + 20,
    discount: Math.floor(Math.random() * 40) + 10,
    isCODAvailable: true,
    isFreeShipping: Math.random() > 0.3,
  }));

  const sortOptions = [
    { value: "popularity", label: "Popularity" },
    { value: "price-low", label: "Price: Low to High" },
    { value: "price-high", label: "Price: High to Low" },
    { value: "newest", label: "Newest First" },
    { value: "rating", label: "Customer Rating" },
  ];

  const filterOptions = {
    color: [
      "Red",
      "Blue",
      "Pink",
      "Green",
      "Yellow",
      "Orange",
      "Purple",
      "Black",
      "White",
    ],
    fabric: ["Cotton", "Silk", "Chiffon", "Georgette", "Velvet", "Satin"],
    occasion: ["Wedding", "Festival", "Party", "Casual", "Formal"],
    size: ["XS", "S", "M", "L", "XL", "XXL", "Free Size"],
  };

  const handleFilterChange = (
    filterType: string,
    value: string,
    checked: boolean,
  ) => {
    setSelectedFilters((prev) => ({
      ...prev,
      [filterType]: checked
        ? [...(prev[filterType] || []), value]
        : (prev[filterType] || []).filter((v) => v !== value),
    }));
  };

  const clearFilters = () => {
    setSelectedFilters({});
    setPriceRange([0, 50000]);
  };

  if (!currentCategory) {
    return (
      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-neutral-900 mb-4">
            Category Not Found
          </h1>
          <Link to="/">
            <Button>Return Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 py-6">
      {/* Breadcrumb */}
      <nav className="flex items-center gap-2 text-sm text-neutral-600 mb-6">
        <Link to="/" className="hover:text-rajasthani-pink">
          Home
        </Link>
        <span>•</span>
        <span className="text-neutral-900 font-medium">
          {currentCategory.title}
        </span>
      </nav>

      {/* Category Header */}
      <div className="mb-8">
        <h1 className="heading-decorative text-2xl sm:text-3xl font-bold text-neutral-900 mb-2">
          {currentCategory.title}
        </h1>
        <p className="text-neutral-600 mb-4">{currentCategory.description}</p>
        <div className="flex items-center gap-4 text-sm text-neutral-600">
          <span>{currentCategory.totalProducts} products</span>
          <TrustBadges variant="horizontal" />
        </div>
      </div>

      {/* Filters & Sort Bar */}
      <div className="flex items-center justify-between gap-4 mb-6 bg-white p-4 rounded-lg shadow-sm">
        <div className="flex items-center gap-4">
          {/* Mobile Filter Sheet */}
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="sm" className="lg:hidden">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </SheetTrigger>
            <SheetContent side="left" className="w-80">
              <SheetHeader>
                <SheetTitle>Filters</SheetTitle>
                <SheetDescription>
                  Refine your search to find the perfect product
                </SheetDescription>
              </SheetHeader>

              <div className="mt-6 space-y-6">
                {/* Price Range */}
                <div>
                  <h3 className="font-semibold mb-3">Price Range</h3>
                  <Slider
                    value={priceRange}
                    onValueChange={setPriceRange}
                    max={50000}
                    step={500}
                    className="mb-2"
                  />
                  <div className="flex justify-between text-sm text-neutral-600">
                    <span>₹{priceRange[0]}</span>
                    <span>₹{priceRange[1]}</span>
                  </div>
                </div>

                {/* Filter Categories */}
                {Object.entries(filterOptions).map(([filterType, options]) => (
                  <div key={filterType}>
                    <h3 className="font-semibold mb-3 capitalize">
                      {filterType}
                    </h3>
                    <div className="space-y-2">
                      {options.map((option) => (
                        <div
                          key={option}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`${filterType}-${option}`}
                            checked={
                              selectedFilters[filterType]?.includes(option) ||
                              false
                            }
                            onCheckedChange={(checked) =>
                              handleFilterChange(
                                filterType,
                                option,
                                checked as boolean,
                              )
                            }
                          />
                          <label
                            htmlFor={`${filterType}-${option}`}
                            className="text-sm cursor-pointer"
                          >
                            {option}
                          </label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}

                <Button
                  onClick={clearFilters}
                  variant="outline"
                  className="w-full"
                >
                  Clear All Filters
                </Button>
              </div>
            </SheetContent>
          </Sheet>

          {/* Desktop Filter Button */}
          <Button variant="outline" size="sm" className="hidden lg:flex">
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </div>

        <div className="flex items-center gap-4">
          {/* Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm">
                Sort by
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {sortOptions.map((option) => (
                <DropdownMenuItem
                  key={option.value}
                  onClick={() => setSortBy(option.value)}
                  className={
                    sortBy === option.value ? "bg-rajasthani-pink/10" : ""
                  }
                >
                  {option.label}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>

          {/* View Mode Toggle */}
          <div className="hidden sm:flex items-center border rounded-lg p-1">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="p-2"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="p-2"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Active Filters */}
      {Object.keys(selectedFilters).length > 0 && (
        <div className="mb-6">
          <div className="flex items-center gap-2 mb-2">
            <span className="text-sm font-medium">Active Filters:</span>
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear All
            </Button>
          </div>
          <div className="flex flex-wrap gap-2">
            {Object.entries(selectedFilters).map(([filterType, values]) =>
              values.map((value) => (
                <div
                  key={`${filterType}-${value}`}
                  className="flex items-center gap-1 px-3 py-1 bg-rajasthani-pink/10 text-rajasthani-pink rounded-full text-sm"
                >
                  <span>{value}</span>
                  <button
                    onClick={() => handleFilterChange(filterType, value, false)}
                    className="ml-1 hover:bg-rajasthani-pink/20 rounded-full p-0.5"
                  >
                    ×
                  </button>
                </div>
              )),
            )}
          </div>
        </div>
      )}

      {/* Products Grid */}
      <div
        className={`grid gap-4 sm:gap-6 ${
          viewMode === "grid"
            ? "grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
            : "grid-cols-1"
        }`}
      >
        {products.map((product) => (
          <Link key={product.id} to={`/product/${product.id}`}>
            <ProductCard
              {...product}
              onAddToCart={(id) => console.log("Add to cart:", id)}
              onToggleWishlist={(id) => console.log("Toggle wishlist:", id)}
            />
          </Link>
        ))}
      </div>

      {/* Load More */}
      <div className="text-center mt-8">
        <Button variant="outline" size="lg">
          Load More Products
        </Button>
      </div>

      {/* SEO Content */}
      <div className="mt-12 bg-white p-6 rounded-lg">
        <h2 className="heading-decorative text-xl font-bold text-neutral-900 mb-4">
          About {currentCategory.title}
        </h2>
        <p className="text-neutral-600 leading-relaxed">
          Discover our exquisite collection of{" "}
          {currentCategory.title.toLowerCase()} that represents the rich
          heritage and vibrant culture of Rajasthan. Each piece is carefully
          crafted by skilled artisans using traditional techniques passed down
          through generations. From intricate embroidery to beautiful mirror
          work, every item tells a story of royal elegance and timeless beauty.
        </p>
      </div>
    </div>
  );
}
