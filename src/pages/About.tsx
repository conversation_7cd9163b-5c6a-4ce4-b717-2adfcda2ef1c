import { Users, Award, Heart, Sparkles, MapPin, Calendar } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function About() {
  const { t } = useLanguage();

  const milestones = [
    {
      year: "1975",
      title: "The Beginning",
      titleHindi: "शुरुआत",
      description:
        "Founded by Shri <PERSON><PERSON> in the heart of Bhadra, Rajasthan",
      descriptionHindi:
        "भद्रा, राजस्थान के दिल में श्री रमन लाल बजावाला द्वारा स्थापित",
    },
    {
      year: "1985",
      title: "Expanding Horizons",
      titleHindi: "विस्तार",
      description: "Started wholesale operations across Rajasthan and Gujarat",
      descriptionHindi: "राजस्थान और गुजरात में थोक व्यापार शुरू किया",
    },
    {
      year: "1995",
      title: "Innovation Era",
      titleHindi: "नवाचार युग",
      description:
        "Introduced modern designs while preserving traditional craftsmanship",
      descriptionHindi:
        "पारंपरिक शिल्प कौशल को संरक्षित रखते हुए आधुनिक डिजाइन पेश किए",
    },
    {
      year: "2010",
      title: "Digital Transformation",
      titleHindi: "डिजिटल परिवर्तन",
      description: "Launched online presence to reach customers nationwide",
      descriptionHindi:
        "देशभर के ग्राहकों तक पहुंचने के लिए ऑनलाइन उपस्थिति शुरू की",
    },
    {
      year: "2020",
      title: "Custom Tailoring",
      titleHindi: "कस्टम सिलाई",
      description: "Introduced personalized tailoring services for perfect fit",
      descriptionHindi: "परफेक्ट फिट के लिए व्यक्तिगत सिलाई सेवाएं शुरू कीं",
    },
    {
      year: "2024",
      title: "New Horizons",
      titleHindi: "नए क्षितिज",
      description:
        "Expanding globally while staying true to our Rajasthani roots",
      descriptionHindi: "राजस्थानी जड़ों से जुड़े रहते हुए वैश्विक विस्तार",
    },
  ];

  const values = [
    {
      icon: Heart,
      title: "Authenticity",
      titleHindi: "प्रामाणिकता",
      description:
        "Every piece reflects genuine Rajasthani heritage and traditional craftsmanship",
      descriptionHindi:
        "हर वस्तु में वास्तविक राजस्थानी विरासत और पारंपरिक शिल्प कौशल झलकता है",
    },
    {
      icon: Users,
      title: "Community",
      titleHindi: "समुदाय",
      description:
        "Supporting local artisans and preserving traditional skills for future generations",
      descriptionHindi:
        "स्थानीय कारीगरों का समर्थन और भावी पीढ़ियों के लिए पारंपरिक कौशल का संरक्षण",
    },
    {
      icon: Sparkles,
      title: "Quality",
      titleHindi: "गुणवत्ता",
      description:
        "Uncompromising commitment to excellence in every thread and stitch",
      descriptionHindi:
        "हर धागे और सिलाई में उत्कृष्टता के लिए अटूट प्रतिबद्धता",
    },
    {
      icon: Award,
      title: "Innovation",
      titleHindi: "नवाचार",
      description:
        "Blending traditional techniques with modern designs and customer needs",
      descriptionHindi:
        "पारंपरिक तकनीकों को आधुनिक डिजाइन और ग्राहक आवश्यकताओं के साथ मिलाना",
    },
  ];

  const team = [
    {
      name: "Raman Lal Bajawala",
      nameHindi: "रमन लाल बजावाला",
      role: "Founder & Visionary",
      roleHindi: "संस्थापक और दूरदर्शी",
      image:
        "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=200&h=200&fit=crop&crop=face",
      description:
        "Started the journey in 1975 with a dream to share Rajasthani heritage with the world",
    },
    {
      name: "Priya Bajawala",
      nameHindi: "प्रिया बजावाला",
      role: "Design Director",
      roleHindi: "डिजाइन निदेशक",
      image:
        "https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=200&h=200&fit=crop&crop=face",
      description:
        "Leads our design team with expertise in traditional and contemporary styles",
    },
    {
      name: "Vikram Bajawala",
      nameHindi: "विक्रम बजावाला",
      role: "Operations Head",
      roleHindi: "संचालन प्रमुख",
      image:
        "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=200&h=200&fit=crop&crop=face",
      description: "Ensures quality and timely delivery of every custom order",
    },
    {
      name: "Master Craftsman Gopal",
      nameHindi: "मास्टर शिल्पकार गोपाल",
      role: "Head Artisan",
      roleHindi: "मुख्य कारीगर",
      image:
        "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?w=200&h=200&fit=crop&crop=face",
      description:
        "40+ years of experience in traditional Rajasthani embroidery and tailoring",
    },
  ];

  const achievements = [
    { number: "50,000+", label: "Happy Customers", labelHindi: "खुश ग्राहक" },
    {
      number: "49",
      label: "Years of Heritage",
      labelHindi: "वर्षों की विरासत",
    },
    { number: "200+", label: "Skilled Artisans", labelHindi: "कुशल कारीगर" },
    { number: "15+", label: "States Served", labelHindi: "राज्यों में सेवा" },
  ];

  return (
    <div className="min-h-screen bg-neutral-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-rajasthani">
        <div className="max-w-7xl mx-auto px-4 text-center">
          <h1 className="heading-decorative text-4xl sm:text-5xl lg:text-6xl font-bold text-neutral-900 mb-6">
            {t("about.heritage.title")}
          </h1>
          <p className="text-xl sm:text-2xl text-hindi text-rajasthani-pink mb-8">
            हमारी विरासत
          </p>
          <div className="max-w-3xl mx-auto">
            <p className="text-lg text-neutral-700 leading-relaxed mb-6">
              {t("about.heritage.text")} We preserve generations of weaving,
              embroidery and block-print craftsmanship, bringing royal
              Rajasthani style to your home.
            </p>
            <p className="text-lg text-neutral-700 text-hindi">
              हम पीढ़ियों से चली आ रही बुनाई, कढ़ाई और ब्लॉक प्रिंट शिल्प कौशल
              का संरक्षण करते हैं, शाही राजस्थानी शैली को आपके घर तक पहुंचाते
              हैं।
            </p>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="absolute top-10 left-10 w-20 h-20 bandhani-dots text-rajasthani-pink/20"></div>
        <div className="absolute bottom-10 right-10 w-16 h-16 bandhani-dots text-rajasthani-orange/20"></div>
      </section>

      {/* Timeline */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
              Our Journey Through Time
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              समय के साथ हमारी यात्रा
            </p>
          </div>

          <div className="relative">
            {/* Timeline Line */}
            <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-rajasthani-pink/30 hidden lg:block"></div>

            <div className="space-y-12 lg:space-y-16">
              {milestones.map((milestone, index) => (
                <div
                  key={milestone.year}
                  className={`flex items-center ${
                    index % 2 === 0 ? "lg:flex-row" : "lg:flex-row-reverse"
                  }`}
                >
                  <div
                    className={`flex-1 ${
                      index % 2 === 0
                        ? "lg:pr-12 lg:text-right"
                        : "lg:pl-12 lg:text-left"
                    }`}
                  >
                    <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                      <div className="flex items-center gap-3 mb-3 lg:justify-start">
                        <div className="w-16 h-16 bg-rajasthani-pink rounded-full flex items-center justify-center">
                          <Calendar className="h-8 w-8 text-white" />
                        </div>
                        <div>
                          <div className="text-2xl font-bold text-rajasthani-pink">
                            {milestone.year}
                          </div>
                        </div>
                      </div>
                      <h3 className="text-xl font-bold text-neutral-900 mb-1">
                        {milestone.title}
                      </h3>
                      <p className="text-lg text-hindi text-rajasthani-pink mb-3">
                        {milestone.titleHindi}
                      </p>
                      <p className="text-neutral-700 mb-2">
                        {milestone.description}
                      </p>
                      <p className="text-sm text-neutral-600 text-hindi">
                        {milestone.descriptionHindi}
                      </p>
                    </div>
                  </div>

                  {/* Timeline Node */}
                  <div className="hidden lg:block w-6 h-6 bg-rajasthani-pink rounded-full border-4 border-white shadow-lg z-10"></div>

                  <div className="flex-1"></div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Values */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
              Our Values
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              हमारे मूल्य
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => {
              const Icon = value.icon;
              return (
                <div key={index} className="text-center group">
                  <div className="w-20 h-20 bg-rajasthani-pink/10 rounded-full flex items-center justify-center mx-auto mb-6 group-hover:bg-rajasthani-pink/20 transition-colors">
                    <Icon className="h-10 w-10 text-rajasthani-pink" />
                  </div>
                  <h3 className="text-xl font-bold text-neutral-900 mb-2">
                    {value.title}
                  </h3>
                  <p className="text-lg text-hindi text-rajasthani-pink mb-4">
                    {value.titleHindi}
                  </p>
                  <p className="text-neutral-700 mb-3 leading-relaxed">
                    {value.description}
                  </p>
                  <p className="text-sm text-neutral-600 text-hindi leading-relaxed">
                    {value.descriptionHindi}
                  </p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Achievements */}
      <section className="py-20 bg-rajasthani-pink/5">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
              Our Achievements
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              हमारी उपलब्धियां
            </p>
          </div>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {achievements.map((achievement, index) => (
              <div key={index} className="text-center">
                <div className="text-4xl sm:text-5xl lg:text-6xl font-bold text-rajasthani-pink mb-2">
                  {achievement.number}
                </div>
                <div className="text-lg font-semibold text-neutral-900 mb-1">
                  {achievement.label}
                </div>
                <div className="text-sm text-hindi text-neutral-600">
                  {achievement.labelHindi}
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Team */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-4">
              Meet Our Team
            </h2>
            <p className="text-lg text-hindi text-rajasthani-pink">
              हमारी टीम से मिलें
            </p>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {team.map((member, index) => (
              <div key={index} className="text-center group">
                <div className="relative mb-6 overflow-hidden rounded-xl">
                  <img
                    src={member.image}
                    alt={member.name}
                    className="w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                </div>
                <h3 className="text-xl font-bold text-neutral-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-lg text-hindi text-rajasthani-pink mb-2">
                  {member.nameHindi}
                </p>
                <p className="text-sm font-semibold text-neutral-700 mb-1">
                  {member.role}
                </p>
                <p className="text-sm text-hindi text-neutral-600 mb-3">
                  {member.roleHindi}
                </p>
                <p className="text-sm text-neutral-600 leading-relaxed">
                  {member.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-20 bg-gradient-rajasthani">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="heading-decorative text-3xl sm:text-4xl font-bold text-neutral-900 mb-8">
            {t("about.mission.title")}
          </h2>
          <p className="text-xl text-hindi text-rajasthani-pink mb-8">
            हमारा मिशन
          </p>
          <div className="space-y-6">
            <p className="text-lg text-neutral-700 leading-relaxed">
              {t("about.mission.text")} We believe that fashion should celebrate
              heritage while embracing the future. Every piece we create is a
              bridge between the timeless artistry of Rajasthan and the
              contemporary world.
            </p>
            <p className="text-lg text-neutral-700 text-hindi leading-relaxed">
              हम मानते हैं कि फैशन को विरासत का जश्न मनाना चाहिए और भविष्य को
              अपनाना चाहिए। हमारी बनाई हर वस्तु राजस्थान की कालातीत कलाकारी और
              समकालीन दुनिया के बीच एक पुल है।
            </p>
          </div>

          <div className="mt-12 p-8 bg-white/80 rounded-2xl backdrop-blur-sm">
            <blockquote className="text-xl italic text-neutral-800 mb-4">
              "राजस्थान की शान, आपके घर की पहचान"
            </blockquote>
            <p className="text-lg text-neutral-700">
              "The pride of Rajasthan, the identity of your home"
            </p>
            <cite className="text-sm text-neutral-600 mt-4 block">
              - Raman Lal Bajawala, Founder
            </cite>
          </div>
        </div>
      </section>

      {/* Location & Contact */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="heading-decorative text-3xl font-bold text-neutral-900 mb-6">
                Visit Our Heritage Store
              </h2>
              <p className="text-lg text-hindi text-rajasthani-pink mb-6">
                हमारे विरासत स्टोर पर आएं
              </p>

              <div className="space-y-4 mb-8">
                <div className="flex items-start gap-3">
                  <MapPin className="h-6 w-6 text-rajasthani-pink mt-1" />
                  <div>
                    <p className="font-semibold text-neutral-900">
                      Bayawala Emporium
                    </p>
                    <p className="text-neutral-700">
                      Bhadra Fort Road, Near Manek Chowk
                      <br />
                      Bhadra, Ahmedabad, Gujarat 380001
                      <br />
                      India
                    </p>
                  </div>
                </div>
              </div>

              <p className="text-neutral-700 leading-relaxed mb-6">
                Step into our heritage store and experience the magic of
                Rajasthani craftsmanship. Our knowledgeable staff will help you
                find the perfect piece and share the stories behind each
                creation.
              </p>

              <p className="text-neutral-700 text-hindi leading-relaxed">
                हमारे विरासत स्टोर में आएं और राजस्थानी शिल्प कौशल के जादू का
                अनुभव करें। हमारे जानकार स्टाफ आपको सही वस्तु खोजने में मदद
                करेंगे और हर रचना की कहानी साझा करेंगे।
              </p>
            </div>

            <div className="bg-neutral-100 rounded-xl overflow-hidden">
              <img
                src="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=600&h=400&fit=crop&crop=center"
                alt="Bayawala Emporium Store"
                className="w-full h-96 object-cover"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
