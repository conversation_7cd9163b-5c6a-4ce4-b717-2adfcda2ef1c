import type { Config } from "tailwindcss";

export default {
  darkMode: ["class"],
  content: [
    "./pages/**/*.{ts,tsx}",
    "./components/**/*.{ts,tsx}",
    "./app/**/*.{ts,tsx}",
    "./src/**/*.{ts,tsx}",
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        sidebar: {
          DEFAULT: "hsl(var(--sidebar-background))",
          foreground: "hsl(var(--sidebar-foreground))",
          primary: "hsl(var(--sidebar-primary))",
          "primary-foreground": "hsl(var(--sidebar-primary-foreground))",
          accent: "hsl(var(--sidebar-accent))",
          "accent-foreground": "hsl(var(--sidebar-accent-foreground))",
          border: "hsl(var(--sidebar-border))",
          ring: "hsl(var(--sidebar-ring))",
        },
        // Rajasthani Color Palette
        rajasthani: {
          pink: "#E62A81", // Barbie Pink
          orange: "#F05C03", // Persimmon/Coral
          yellow: "#F0E245", // Mustard Yellow
          gold: "#D4AF37", // Antique Gold
          cream: "#FDF6E3", // Warm Cream
          beige: "#F5F5DC", // Light Beige
          sand: "#F4A460", // Sandy Brown
        },
        // Base neutral colors for backgrounds
        neutral: {
          50: "#FDFCF9", // Off white
          100: "#F8F6F0", // Light cream
          200: "#F0EDE5", // Beige
          300: "#E8E2D5", // Light sand
          400: "#D3C7B8", // Medium sand
          500: "#A68B5B", // Dark sand
          600: "#8B7355", // Brown
          700: "#6B5B47", // Dark brown
          800: "#4A3F2A", // Very dark brown
          900: "#2D1B0E", // Almost black
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
        shimmer: {
          "0%": { transform: "translateX(-100%)" },
          "100%": { transform: "translateX(100%)" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
        shimmer: "shimmer 2s infinite",
      },
      fontFamily: {
        hindi: ["Noto Sans Devanagari", "Arial", "sans-serif"],
        decorative: ["Playfair Display", "serif"],
      },
      backgroundImage: {
        "bandhani-dots":
          "radial-gradient(circle at 50% 50%, currentColor 1px, transparent 1px)",
        "textile-pattern":
          "repeating-conic-gradient(from 0deg at 50% 50%, transparent 0deg, currentColor 1deg, transparent 2deg)",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config;
